import React, { useState, useCallback } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  Image,
  TouchableOpacity,
  BackHandler,
  Platform,
  Dimensions,
  KeyboardAvoidingView,
  ImageBackground,
  TextInput
} from "react-native";
import { Colors, Fonts, Default } from "../../constants/styles";
import SnackbarToast from "../../components/snackbarToast";
//import IntlPhoneInput from "react-native-intl-phone-input";
import { useTranslation } from "react-i18next";
import Loader from "../../components/loader";
import { useFocusEffect } from "@react-navigation/native";
import MyStatusBar from "../../components/myStatusBar";
import { useNavigation } from "expo-router";
import { OtpInput } from "react-native-otp-entry";

const { width, height } = Dimensions.get("window");

const LoginScreen = () => {
  const navigation = useNavigation();

  const { t, i18n } = useTranslation();

  const isRtl = i18n.dir() == "rtl";

  function tr(key) {
    return t(`loginScreen:${key}`);
  }
  const [visibleToast, setVisibleToast] = useState(false);
  const onDismissVisibleToast = () => setVisibleToast(!visibleToast);

  /*// Phone number validation states
  const [phoneNumber, setPhoneNumber] = useState("");
  const [phoneError, setPhoneError] = useState("");*/
  const [loginCode, setLoginCode] = useState("");
  const [codeError, setCodeError] = useState("");

  const [exitApp, setExitApp] = useState(0);
  useFocusEffect(
    useCallback(() => {
      const backAction = () => {
        if (Platform.OS === "android") {
          setTimeout(() => {
            setExitApp(0);
          }, 2000);

          if (exitApp === 0) {
            setExitApp(exitApp + 1);
            setVisibleToast(true);
          } else if (exitApp === 1) {
            BackHandler.exitApp();
          }
          return true;
        }
      };
      BackHandler.addEventListener("hardwareBackPress", backAction);
      return () => {
        BackHandler.removeEventListener("hardwareBackPress", backAction);
      };
    }, [exitApp])
  );

  const [loginLoaderVisible, setLoginLoaderVisible] = useState(false);

  /*// Phone number validation function
  const validatePhoneNumber = (number) => {
    // Remove any spaces, dashes, or other non-digit characters
    const cleanNumber = number.replace(/\D/g, '');
    
    // Check if it's exactly 9 digits and starts with 6 or 7
    if (cleanNumber.length !== 9) {
      return "Phone number must be exactly 9 digits";
    }
    
    if (!cleanNumber.startsWith('6') && !cleanNumber.startsWith('7')) {
      return "Phone number must start with 6 or 7";
    }
    
    return null; // Valid
  };*/
  const validateLoginCode = (code) => {
    if (code.length !== 6) {
      return "Please enter a 6-digit code";
    }
    if (!/^\d{6}$/.test(code)) {
      return "Code must contain only numbers";
    }
    return null; // Valid
  };

  /*const handlePhoneNumberChange = (data) => {
    const { unmaskedPhoneNumber } = data;
    setPhoneNumber(unmaskedPhoneNumber);
    
    if (phoneError) {
      setPhoneError("");
    }
  };*/
 const handleCodeChange = (text) => {
  setLoginCode(text);
  
  // Clear error when user starts typing
  if (codeError) {
    setCodeError("");
  }
  
  if (text.length === 6) {
    handleLoginBtn();
  }
};

 /* const handleLoginBtn = () => {
    const validationError = validatePhoneNumber(phoneNumber);
    
    if (validationError) {
      setPhoneError(validationError);
      return;
    }

    setLoginLoaderVisible(true);
    setTimeout(() => {
      setLoginLoaderVisible(false);
      navigation.push("auth/otpScreen");
    }, 800);
  };*/
  const handleLoginBtn = () => {
    setLoginLoaderVisible(true);
    
    /*// TODO: Send code to backend for validation
        try {
        // This is where you'll make the server request
        const response = await fetch('API_ENDPOINT', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            loginCode: loginCode
          })
        });
        
        const result = await response.json();
        
        if (result.success) {
          navigation.push("(tabs)"); // Skip OTP, PIN, going directly to tabs

        } else {
          setCodeError("Invalid login code");
        }
      } catch (error) {
        setCodeError("Network error. Please try again.");
      } finally {
        setLoginLoaderVisible(false);
      }
    */
    // For now, accept any 6-digit code
    setTimeout(() => {
      setLoginLoaderVisible(false);
      navigation.push("(tabs)"); 
    }, 800);
  };

  const screenBackground = () => {
    return (
      <View
        style={{
          position: "absolute",
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        }}
      >
        <View
          style={{
            flex: 1,
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <ImageBackground
            source={require("../../assets/images/bg.png")}
            style={{
              width: width,
              height: height * 0.3,
            }}
          />

          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
              marginBottom: Default.fixPadding * 2,
            }}
          >
            <Image
              source={require("../../assets/images/splashIcon.png")}
              style={{ width: 78, height: 78, tintColor: Colors.primary }}
            />
            <Text style={{ ...Fonts.SemiBold25primary }}>SafariBank</Text>
          </View>
        </View>
      </View>
    );
  };
  return (
    <View
      style={{
        flex: 1,
      }}
    >
      <MyStatusBar />
      <View style={{ flex: 1, backgroundColor: Colors.white }}>
        {screenBackground()}

        <Text
          style={{
            ...Fonts.Bold25white,
            textAlign: "center",
            marginTop: Default.fixPadding * 6,
          }}
        >
          {tr("login")}
        </Text>

        <View
          style={{
            maxHeight: height / 1.8,
            marginTop: Default.fixPadding * 3.2,
            marginBottom: Default.fixPadding * 2,
            marginHorizontal: Default.fixPadding * 2,
            borderRadius: 50,
            backgroundColor: Colors.white,
            ...Default.shadow,
          }}
        >
          <View
            style={{
              overflow: "hidden",
              borderRadius: 50,
            }}
          >
            
              <Image
                source={require("../../assets/images/image.png")}
                style={{
                  alignSelf: "center",
                  width: 140,
                  height: 140,
                  marginVertical: Default.fixPadding * 1.5,
                }}
              />
              <View style={{ justifyContent: "center", alignItems: "center" }}>
                <Text style={{ ...Fonts.Bold18black }}>
                  {tr("welcomeBack")}
                </Text>
                <Text style={{ ...Fonts.SemiBold14grey }}>{tr("happy")}</Text>
              </View>

             {/*<IntlPhoneInput
                defaultCountry="TZ"
                closeText={tr("close")}
                filterText={tr("search")}
                placeholder={tr("mobileNumber")}
                placeholderTextColor={Colors.grey}
                onChangeText={handlePhoneNumberChange}
                flagStyle={{
                  height: 0,
                  width: 0,
                }}
                inputProps={{
                  selectionColor: Colors.primary,
                }}
                modalCountryItemCountryNameStyle={{
                  ...Fonts.SemiBold16black,
                }}
                closeButtonStyle={{
                  ...Fonts.SemiBold16black,
                  backgroundColor: Colors.primary,
                }}
                dialCodeTextStyle={{
                  ...Fonts.SemiBold16black,
                  paddingRight: Default.fixPadding * 1.2,
                }}
                containerStyle={{
                  alignItems: "center",
                  justifyContent: "center",
                  paddingVertical: Default.fixPadding,
                  paddingHorizontal: Default.fixPadding * 1.5,
                  marginHorizontal: Default.fixPadding * 2,
                  marginVertical: Default.fixPadding * 4,
                  borderRadius: 10,
                  backgroundColor: Colors.white,
                  borderWidth: phoneError ? 1 : 0,
                  borderColor: phoneError ? Colors.red : 'transparent',
                  ...Default.shadow,
                }}
                phoneInputStyle={{
                  padding: 0,
                  ...Fonts.SemiBold16black,
                  textAlign: isRtl ? "right" : "left",
                  paddingHorizontal: isRtl ? 0 : Default.fixPadding * 1.2,
                  borderLeftWidth: 2,
                  borderLeftColor: Colors.lightGrey,
                }}
              />*/}
              <View
                style={{
                  marginHorizontal: Default.fixPadding * 4.5,
                  marginVertical: Default.fixPadding * 4,
                }}
              >
                <OtpInput
                  numberOfDigits={6}
                  onTextChange={handleCodeChange}
                  theme={{
                    pinCodeContainerStyle: {
                      borderWidth: 0,
                      width: 48,
                      height: 48,
                      borderRadius: 10,
                      backgroundColor: Colors.white,
                      ...Default.shadow,
                    },
                    pinCodeTextStyle: { ...Fonts.SemiBold22primary },
                    focusedPinCodeContainerStyle: {
                      borderWidth: 0,
                      borderRadius: 10,
                    },
                    focusStickStyle: { backgroundColor: Colors.primary },
                  }}
                />
              </View>
              {/* Error message display --phone
              {phoneError ? (
                <Text style={styles.errorText}>
                  {phoneError}
                </Text>
              ) : null}*/}
              {codeError ? (
                  <Text style={styles.errorText}>
                    {codeError}
                  </Text>
              ) : null}
              
              <Loader visible={loginLoaderVisible} />
              <TouchableOpacity
                onPress={handleLoginBtn}
                style={[
                  styles.loginBtn,
                ]}
              >
                <Text style={{ ...Fonts.Bold18white }}>{tr("login")}</Text>
              </TouchableOpacity>
            

            {/*<TouchableOpacity
              onPress={() => navigation.push("auth/registerScreen")}
              style={styles.registerLink}
            >
              <Text style={{ ...Fonts.SemiBold14primary, textAlign: "center", paddingTop: Default.fixPadding * 2 }}>
                {tr("noAccount")} {tr("registerHere")}
              </Text>
            </TouchableOpacity>*/}
          </View>
        </View>
        <SnackbarToast
          title={tr("tapBack")}
          visible={visibleToast}
          onDismiss={onDismissVisibleToast}
        />
      </View>
    </View>
  );
};

export default LoginScreen;

const styles = StyleSheet.create({
  loginBtn: {
    justifyContent: "center",
    alignItems: "center",
    padding: Default.fixPadding * 1.2,
    marginTop: Default.fixPadding,
    marginBottom: Default.fixPadding * 3,
    marginHorizontal: Default.fixPadding * 2,
    borderRadius: 10,
    backgroundColor: Colors.primary,
    ...Default.shadowBtn,
  },
  errorText: {
    color: Colors.red || '#FF0000',
    fontSize: 12,
    textAlign: "center",
    marginTop: -Default.fixPadding * 2,
    marginBottom: Default.fixPadding,
    marginHorizontal: Default.fixPadding * 2,
  },
});