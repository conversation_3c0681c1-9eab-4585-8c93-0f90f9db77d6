{"artifacts": [{"path": "C:/Users/<USER>/Desktop/safaribank/android/app/build/intermediates/cxx/Debug/6c3h02c1/obj/arm64-v8a/libappmodules.so"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "target_compile_options", "target_include_directories"], "files": ["C:/Users/<USER>/Desktop/safaribank/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake", "CMakeLists.txt", "C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt", "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 31, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 56, "parent": 2}, {"command": 2, "file": 0, "line": 101, "parent": 2}, {"command": 2, "file": 0, "line": 87, "parent": 2}, {"command": 3, "file": 0, "line": 63, "parent": 2}, {"command": 4, "file": 0, "line": 58, "parent": 2}, {"file": 2}, {"command": 4, "file": 2, "line": 77, "parent": 8}, {"file": 3}, {"command": 4, "file": 3, "line": 82, "parent": 10}, {"file": 4}, {"command": 4, "file": 4, "line": 80, "parent": 12}, {"file": 5}, {"command": 4, "file": 5, "line": 83, "parent": 14}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 6, "fragment": "-Wall"}, {"backtrace": 6, "fragment": "-Werror"}, {"backtrace": 6, "fragment": "-Wno-error=cpp"}, {"backtrace": 6, "fragment": "-fexceptions"}, {"backtrace": 6, "fragment": "-frtti"}, {"backtrace": 6, "fragment": "-std=c++20"}, {"backtrace": 6, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 6, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 4, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 4, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 4, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "appmodules_EXPORTS"}], "includes": [{"backtrace": 7, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, {"backtrace": 7, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/build/generated/autolinking/src/main/jni"}, {"backtrace": 9, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni"}, {"backtrace": 11, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni"}, {"backtrace": 13, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni"}, {"backtrace": 15, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/safaribank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/d20a2db9ec5e7f48674b6f3c79a8b161/transformed/fbjni-0.6.0/prefab/modules/fbjni/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/jsi/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/735ffdc777bf493d1b59bb9260f05d0b/transformed/react-android-0.76.6-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "dependencies": [{"backtrace": 4, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe"}, {"backtrace": 4, "id": "react_codegen_rnpicker::@e8bb2e9e833f47d0d516"}, {"backtrace": 4, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec"}, {"backtrace": 4, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a"}, {"backtrace": 4, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad"}, {"backtrace": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a"}, {"backtrace": 4, "id": "react_codegen_pagerview::@7032a8921530ec438d60"}, {"backtrace": 4, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c"}, {"backtrace": 4, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65"}, {"backtrace": 4, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d"}], "id": "appmodules::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 4, "fragment": "\"C:\\Users\\<USER>\\Desktop\\safaribank\\android\\app\\build\\intermediates\\cxx\\Debug\\6c3h02c1\\obj\\arm64-v8a\\libreact_codegen_rnpicker.so\"", "role": "libraries"}, {"backtrace": 4, "fragment": "\"C:\\Users\\<USER>\\Desktop\\safaribank\\android\\app\\build\\intermediates\\cxx\\Debug\\6c3h02c1\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so\"", "role": "libraries"}, {"backtrace": 4, "fragment": "\"C:\\Users\\<USER>\\Desktop\\safaribank\\android\\app\\build\\intermediates\\cxx\\Debug\\6c3h02c1\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so\"", "role": "libraries"}, {"backtrace": 4, "fragment": "\"C:\\Users\\<USER>\\Desktop\\safaribank\\android\\app\\build\\intermediates\\cxx\\Debug\\6c3h02c1\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so\"", "role": "libraries"}, {"backtrace": 5, "fragment": "\"C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d20a2db9ec5e7f48674b6f3c79a8b161\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so\"", "role": "libraries"}, {"backtrace": 5, "fragment": "\"C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\735ffdc777bf493d1b59bb9260f05d0b\\transformed\\react-android-0.76.6-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so\"", "role": "libraries"}, {"backtrace": 5, "fragment": "\"C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\735ffdc777bf493d1b59bb9260f05d0b\\transformed\\react-android-0.76.6-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so\"", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "appmodules", "nameOnDisk": "libappmodules.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "Object Libraries", "sourceIndexes": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "OnLoad.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/pagerview-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/pagerviewJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}