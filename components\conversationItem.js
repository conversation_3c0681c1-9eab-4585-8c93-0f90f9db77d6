import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Colors, Default, Fonts } from '../constants/styles';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

const ConversationItem = ({ conversation, onPress, isRtl }) => {
  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now - date) / (1000 * 60));
      return diffInMinutes < 1 ? 'Now' : `${diffInMinutes}m`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  const getAvatarColor = (type) => {
    switch (type) {
      case 'support':
        return Colors.primary;
      case 'system':
        return '#2196f3';
      case 'transaction':
        return '#ff9800';
      default:
        return Colors.grey;
    }
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      style={{
        flexDirection: isRtl ? 'row-reverse' : 'row',
        alignItems: 'center',
        paddingHorizontal: Default.fixPadding * 2,
        paddingVertical: Default.fixPadding * 1.5,
        backgroundColor: Colors.white,
        borderBottomWidth: 1,
        borderBottomColor: Colors.extraLightGrey,
      }}
    >
      {/* Avatar */}
      <View
        style={{
          width: 50,
          height: 50,
          borderRadius: 25,
          backgroundColor: getAvatarColor(conversation.type),
          justifyContent: 'center',
          alignItems: 'center',
          marginRight: isRtl ? 0 : Default.fixPadding * 1.5,
          marginLeft: isRtl ? Default.fixPadding * 1.5 : 0,
        }}
      >
        <MaterialCommunityIcons
          name={conversation.avatar}
          size={24}
          color={Colors.white}
        />
      </View>

      {/* Content */}
      <View style={{ flex: 1 }}>
        <View
          style={{
            flexDirection: isRtl ? 'row-reverse' : 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: Default.fixPadding * 0.5,
          }}
        >
          <Text
            style={{
              ...Fonts.SemiBold16black,
              flex: 1,
              textAlign: isRtl ? 'right' : 'left',
            }}
            numberOfLines={1}
          >
            {conversation.title}
          </Text>
          
          <Text
            style={{
              ...Fonts.Regular12grey,
              marginLeft: isRtl ? 0 : Default.fixPadding,
              marginRight: isRtl ? Default.fixPadding : 0,
            }}
          >
            {formatTime(conversation.lastMessageTime)}
          </Text>
        </View>

        <View
          style={{
            flexDirection: isRtl ? 'row-reverse' : 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Text
            style={{
              ...Fonts.Regular14grey,
              flex: 1,
              textAlign: isRtl ? 'right' : 'left',
            }}
            numberOfLines={1}
          >
            {conversation.lastMessage || conversation.subtitle}
          </Text>
          
          {conversation.unreadCount > 0 && (
            <View
              style={{
                backgroundColor: Colors.primary,
                borderRadius: 10,
                minWidth: 20,
                height: 20,
                justifyContent: 'center',
                alignItems: 'center',
                paddingHorizontal: 6,
                marginLeft: isRtl ? 0 : Default.fixPadding,
                marginRight: isRtl ? Default.fixPadding : 0,
              }}
            >
              <Text
                style={{
                  ...Fonts.SemiBold12white,
                  fontSize: 10,
                }}
              >
                {conversation.unreadCount > 99 ? '99+' : conversation.unreadCount}
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default ConversationItem;