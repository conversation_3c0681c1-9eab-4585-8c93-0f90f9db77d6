{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4b5fbf6908f3e082e261c13278e081e6\\transformed\\play-services-base-18.5.0\\res\\values-km\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,442,559,660,818,938,1055,1160,1314,1427,1594,1715,1856,2010,2070,2124", "endColumns": "97,150,116,100,157,119,116,104,153,112,166,120,140,153,59,53,72", "endOffsets": "290,441,558,659,817,937,1054,1159,1313,1426,1593,1714,1855,2009,2069,2123,2196"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4376,4478,4633,4754,4859,5021,5145,5266,5524,5682,5799,5970,6095,6240,6398,6462,6520", "endColumns": "101,154,120,104,161,123,120,108,157,116,170,124,144,157,63,57,76", "endOffsets": "4473,4628,4749,4854,5016,5140,5261,5370,5677,5794,5965,6090,6235,6393,6457,6515,6592"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a43c68922964e2a138ddfb9cf7287a27\\transformed\\core-1.13.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "35,36,37,38,39,40,41,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3115,3210,3313,3411,3511,3612,3724,13629", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "3205,3308,3406,3506,3607,3719,3831,13725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1e754318e29fb6f6717a4ed39daeac16\\transformed\\play-services-basement-18.4.0\\res\\values-km\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "144", "endOffsets": "339"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5375", "endColumns": "148", "endOffsets": "5519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0218db25d639d0bf937ef580de527e1b\\transformed\\material-1.6.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,304,404,516,596,661,755,825,887,974,1039,1098,1163,1224,1281,1400,1458,1519,1576,1647,1777,1863,1941,2049,2124,2195,2292,2359,2425,2505,2595,2681,2760,2837,2907,2982,3070,3140,3240,3339,3413,3489,3596,3650,3723,3814,3910,3972,4036,4099,4198,4296,4388,4488", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,99,111,79,64,93,69,61,86,64,58,64,60,56,118,57,60,56,70,129,85,77,107,74,70,96,66,65,79,89,85,78,76,69,74,87,69,99,98,73,75,106,53,72,90,95,61,63,62,98,97,91,99,82", "endOffsets": "219,299,399,511,591,656,750,820,882,969,1034,1093,1158,1219,1276,1395,1453,1514,1571,1642,1772,1858,1936,2044,2119,2190,2287,2354,2420,2500,2590,2676,2755,2832,2902,2977,3065,3135,3235,3334,3408,3484,3591,3645,3718,3809,3905,3967,4031,4094,4193,4291,4383,4483,4566"}, "to": {"startLines": "2,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3035,3836,3936,4048,6984,7049,7545,7936,8064,8151,8216,8275,8340,8401,8458,8577,8635,8696,8753,8824,9176,9262,9340,9448,9523,9594,9691,9758,9824,9904,9994,10080,10159,10236,10306,10381,10469,10539,10639,10738,10812,10888,10995,11049,11122,11213,11309,11371,11435,11498,11597,11695,11787,12059", "endLines": "5,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "endColumns": "12,79,99,111,79,64,93,69,61,86,64,58,64,60,56,118,57,60,56,70,129,85,77,107,74,70,96,66,65,79,89,85,78,76,69,74,87,69,99,98,73,75,106,53,72,90,95,61,63,62,98,97,91,99,82", "endOffsets": "269,3110,3931,4043,4123,7044,7138,7610,7993,8146,8211,8270,8335,8396,8453,8572,8630,8691,8748,8819,8949,9257,9335,9443,9518,9589,9686,9753,9819,9899,9989,10075,10154,10231,10301,10376,10464,10534,10634,10733,10807,10883,10990,11044,11117,11208,11304,11366,11430,11493,11592,11690,11782,11882,12137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b7a298e4f795a18f3d41262269705afc\\transformed\\browser-1.6.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,152,249,382", "endColumns": "96,96,132,99", "endOffsets": "147,244,377,477"}, "to": {"startLines": "66,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6597,7143,7240,7373", "endColumns": "96,96,132,99", "endOffsets": "6689,7235,7368,7468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\960e1087eeccdf840de6823a1e7fc27f\\transformed\\ui-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,190,270,374,472,560,644,727,812,899,979,1064,1140,1214,1286,1357,1441,1507", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,73,71,70,83,65,117", "endOffsets": "185,265,369,467,555,639,722,807,894,974,1059,1135,1209,1281,1352,1436,1502,1620"}, "to": {"startLines": "45,46,67,68,69,79,80,131,132,136,137,141,145,148,150,155,156,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4128,4213,6694,6798,6896,7769,7853,11887,11972,12305,12385,12702,13020,13252,13395,13809,13893,14033", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,73,71,70,83,65,117", "endOffsets": "4208,4288,6793,6891,6979,7848,7931,11967,12054,12380,12465,12773,13089,13319,13461,13888,13954,14146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb5951a603c6ef86f8f4868bb3898655\\transformed\\react-android-0.76.9-debug\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,283,353,437,503,572,648,725,808,888,959,1036,1118,1195,1278,1360,1436,1507,1577,1670,1749,1823,1902", "endColumns": "72,82,71,69,83,65,68,75,76,82,79,70,76,81,76,82,81,75,70,69,92,78,73,78,76", "endOffsets": "123,206,278,348,432,498,567,643,720,803,883,954,1031,1113,1190,1273,1355,1431,1502,1572,1665,1744,1818,1897,1974"}, "to": {"startLines": "33,47,75,77,78,82,95,96,97,134,135,138,139,142,143,144,146,147,149,151,152,154,157,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2962,4293,7473,7615,7685,7998,8954,9023,9099,12142,12225,12470,12541,12778,12860,12937,13094,13176,13324,13466,13536,13730,13959,14151,14230", "endColumns": "72,82,71,69,83,65,68,75,76,82,79,70,76,81,76,82,81,75,70,69,92,78,73,78,76", "endOffsets": "3030,4371,7540,7680,7764,8059,9018,9094,9171,12220,12300,12536,12613,12855,12932,13015,13171,13247,13390,13531,13624,13804,14028,14225,14302"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6f597dc2fa8134c9a491d7aedff8ce\\transformed\\foundation-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,146", "endColumns": "90,91", "endOffsets": "141,233"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "14307,14398", "endColumns": "90,91", "endOffsets": "14393,14485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\73718788808fe748888117607a7f7695\\transformed\\appcompat-1.7.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,376,475,585,672,775,896,974,1050,1141,1234,1326,1420,1520,1613,1708,1802,1893,1984,2067,2171,2275,2375,2484,2593,2702,2864,12618", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "371,470,580,667,770,891,969,1045,1136,1229,1321,1415,1515,1608,1703,1797,1888,1979,2062,2166,2270,2370,2479,2588,2697,2859,2957,12697"}}]}]}