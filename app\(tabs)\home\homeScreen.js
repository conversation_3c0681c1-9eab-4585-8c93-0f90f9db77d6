import {
  StyleSheet,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  Dimensions,
  ImageBackground,
  Image,
} from "react-native";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Colors, Default, Fonts } from "../../../constants/styles";
import Ionicons from "react-native-vector-icons/Ionicons";
import MyStatusBar from "../../../components/myStatusBar";
import { useNavigation } from "expo-router";
import { LineChart } from "react-native-chart-kit";

const { width } = Dimensions.get("window");
const CARD_WIDTH = width * 0.8;

const HomeScreen = () => {
  const navigation = useNavigation();
  const { t, i18n } = useTranslation();
  const isRtl = i18n.dir() == "rtl";

  function tr(key) {
    return t(`homeScreen:${key}`);
  }

  const [chartData, setChartData] = useState({
    labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
    datasets: [
      {
        data: [40, 30, 50, 20, 60, 70, 80, 50, 40, 30, 20, 10],
      },
    ],
  });

  const [notificationCount, setNotificationCount] = useState(0);

  const accountDetailList = [
    {
      key: "1",
      name: "Saving account",
      balance: "Tsh 15000",
      acNumber: "A/c no ********",
    },
    {
      key: "2",
      name: "Current account",
      balance: "Tsh 5000",
      acNumber: "A/c no ********",
    },
  ];

  const renderItemAccountDetail = ({ item }) => {
    return (
      <ImageBackground
        resizeMode="stretch"
        source={require("../../../assets/images/image2.png")}
        style={{
          marginVertical: Default.fixPadding * 3,
          marginHorizontal: Default.fixPadding,
          width: CARD_WIDTH,
          height: 120,
        }}
      >
        <View
          style={{
            alignItems: isRtl ? "flex-end" : "flex-start",
            padding: Default.fixPadding * 1.7,
          }}
        >
          <Text
            numberOfLines={1}
            style={{ ...Fonts.Bold18extraLightRegularGrey }}
          >
            {`${tr("totalBalance")} : `}
            <Text style={{ ...Fonts.Bold22extraLightRegularGrey }}>
              {item.balance}
            </Text>
          </Text>

          <Text
            numberOfLines={1}
            style={{
              ...Fonts.SemiBold14extraLightPink,
              marginTop: Default.fixPadding * 1.5,
            }}
          >
            {item.name}
          </Text>
          <Text
            numberOfLines={1}
            style={{
              ...Fonts.Bold14extraLightPink,
              marginTop: Default.fixPadding * 0.3,
            }}
          >
            {item.acNumber}
          </Text>
        </View>
      </ImageBackground>
    );
  };

  const servicesList = [
    {
      key: "1",
      image: require("../../../assets/images/Services1.png"),
      title: tr("account"),
    },
    {
      key: "2",
      image: require("../../../assets/images/Services2.png"),
      title: tr("fundTransfer"),
    },
    {
      key: "3",
      image: require("../../../assets/images/Services3.png"),
      title: tr("statement"),
    },
    {
      key: "4",
      image: require("../../../assets/images/Services5.png"),
      title: tr("invoices"),
    },
    {
      key: "5",
      image: require("../../../assets/images/Services4.png"),
      title: tr("withdraw"),
    },
    {
      key: "6",
      image: require("../../../assets/images/Services6.png"),
      title: tr("more"),
    },
  ];

  const servicesClickHandler = (index) => {
    if (index == 0) {
      return navigation.push("accountDetail/accountDetailScreen");
    } else if (index == 1) {
      return navigation.push("fundTransfer/fundTransferScreen");
    } else if (index == 2) {
      return navigation.push("statement/statementScreen");
    } else if (index == 3) {
      return navigation.push("addDeposit/addDepositScreen");
    } else if (index == 4) {
      return navigation.push("withdraw/withdrawScreen");
    } else if (index == 5) {
      return navigation.push("services/servicesScreen");
    }
  };

  const renderItemServices = ({ item, index }) => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        disabled={false}
        onPress={() => servicesClickHandler(index)}
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          paddingVertical: Default.fixPadding * 2,
          paddingHorizontal: Default.fixPadding * 0.3,
          marginHorizontal: Default.fixPadding,
          marginBottom: Default.fixPadding * 2,
          borderRadius: 10,
          backgroundColor: Colors.white,
          ...Default.shadow,
        }}
      >
        <Image source={item.image} style={{ width: 30, height: 30 }} />
        <Text
          numberOfLines={1}
          style={{
            ...(index === 5 ? Fonts.Bold15grey : Fonts.Bold15primary),
            overflow: "hidden",
            marginTop: Default.fixPadding * 0.5,
          }}
        >
          {item.title}
        </Text>
      </TouchableOpacity>
    );
  };

  const transactionList = [
    {
      key: "1",
      image: require("../../../assets/images/Services2.png"),
      name: "Jeklin shah",
      other: "Money transfer",
      dollar: "-$140",
      transaction: false,
    },
    {
      key: "2",
      image: require("../../../assets/images/transaction1.png"),
      name: "Paypal",
      other: "Deposits",
      dollar: "-$140",
      transaction: true,
    },
    {
      key: "3",
      image: require("../../../assets/images/transaction2.png"),
      name: "Amazon",
      other: "Online payment",
      dollar: "-$140",
      transaction: false,
    },
  ];

  const PerformanceDashboard = () => {
    return (
      <View style={styles.performanceDashboard}>
        <View style={styles.performanceRow}>
          <View style={styles.performanceBox}>
            <Text style={styles.performanceTitle}>Total Income</Text>
            <Text style={styles.performanceValue}>Tsh 3,729</Text>
            <Text style={styles.performanceChange}>17.3% vs Previous year</Text>
          </View>
          <View style={styles.performanceBox}>
            <Text style={styles.performanceTitle}>Total Expenses</Text>
            <Text style={styles.performanceValue}>Tsh 347</Text>
            <Text style={styles.performanceChange}>27.1% vs Previous year</Text>
          </View>
        </View>
        <View style={styles.performanceRow}>
          <View style={styles.performanceGauge}>
            <Text style={styles.performanceGaugeValue}>1.38</Text>
            <Text style={styles.performanceGaugeLabel}>Quick Ratio</Text>
            <Text style={styles.performanceGaugeLearnMore}>Learn More</Text>
          </View>
          <View style={styles.performanceGauge}>
            <Text style={styles.performanceGaugeValue}>19%</Text>
            <Text style={styles.performanceGaugeLabel}>New Profit Margin</Text>
            <Text style={styles.performanceGaugeLearnMore}>Learn More</Text>
          </View>
        </View>
        <LineChart
          data={chartData}
          width={width - Default.fixPadding * 6}
          height={220}
          chartConfig={{
            backgroundColor: Colors.white,
            backgroundGradientFrom: Colors.white,
            backgroundGradientTo: Colors.white,
            decimalPlaces: 2,
            color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            style: {
              borderRadius: 16,
            },
            propsForDots: {
              r: "6",
              strokeWidth: "2",
              stroke: Colors.primary,
            },
          }}
          style={{
            marginVertical: 8,
            borderRadius: 16,
          }}
        />
      </View>
    );
  };

  const ListFooterComponent = () => {
    return (
      <View>
        <View
          style={{
            flexDirection: isRtl ? "row-reverse" : "row",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: Default.fixPadding,
            marginHorizontal: Default.fixPadding * 2,
          }}
        >
          <Text
            numberOfLines={1}
            style={{
              flex: 1,
              textAlign: isRtl ? "right" : "left",
              marginRight: isRtl ? 0 : Default.fixPadding,
              marginLeft: isRtl ? Default.fixPadding : 0,
              ...Fonts.Bold18black,
            }}
          >
            {tr("businessSummary")}
          </Text>
          <TouchableOpacity
            onPress={() =>
              navigation.push("latestTransaction/latestTransactionScreen")
            }
          >
            <Text
              numberOfLines={1}
              style={{ maxWidth: 100, ...Fonts.Bold14grey }}
            >
              {tr("seeAll")}
            </Text>
          </TouchableOpacity>
        </View>

        <PerformanceDashboard />
      </View>
    );
  };

  return (
    <View style={{ flex: 1 }}>
      <MyStatusBar />
      <View style={{ flex: 1, backgroundColor: Colors.regularGrey }}>
        <Image
          source={require("../../../assets/images/homeImage.png")}
          style={{ width: width, height: 220 }}
        />
        <View style={{ position: "absolute" }}>
          <View
            style={{
              flexDirection: isRtl ? "row-reverse" : "row",
              justifyContent: "center",
              alignItems: "center",
              paddingTop: Default.fixPadding * 1.2,
              paddingHorizontal: Default.fixPadding * 2,
            }}
          >
            <View
              style={{
                flex: 1,
                flexDirection: isRtl ? "row-reverse" : "row",
                alignItems: "center",
              }}
            >
              <Image
                source={require("../../../assets/images/splashIcon.png")}
                style={{ width: 25, height: 25 }}
              />
              <Text
                numberOfLines={1}
                style={{
                  flex: 1,
                  textAlign: isRtl ? "right" : "left",
                  ...Fonts.Bold20white,
                  marginHorizontal: Default.fixPadding * 0.5,
                }}
              >
                SafariBank
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => navigation.push("notification/notificationScreen")}
              style={{ marginRight: Default.fixPadding, position: 'relative' }}
            >
              <Ionicons
                name="notifications-outline"
                size={25}
                color={Colors.white}
              />
              {notificationCount >= 0 && (
                <View
                  style={{
                    position: 'absolute',
                    top: -5,
                    right: -5,
                    backgroundColor: Colors.red,
                    borderRadius: 10,
                    minWidth: 20,
                    height: 20,
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderWidth: 2,
                    borderColor: Colors.white,
                  }}
                >
                  <Text
                    style={{
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: 'bold',
                      textAlign: 'center',
                    }}
                  >
                    {notificationCount > 99 ? '99+' : notificationCount}
                  </Text>
                </View>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => navigation.push("editProfile/accountScreen")}
              style={{ marginLeft: 10 }}
            >
              <Ionicons
                name="person-outline"
                size={25}
                color={Colors.white}
              />
            </TouchableOpacity>
          </View>

          <FlatList
            inverted={isRtl}
            horizontal
            pagingEnabled
            snapToAlignment="center"
            decelerationRate={"fast"}
            scrollEventThrottle={1}
            data={accountDetailList}
            keyExtractor={(item) => item.key}
            renderItem={renderItemAccountDetail}
            showsHorizontalScrollIndicator={false}
            snapToInterval={CARD_WIDTH + Default.fixPadding * 1.4}
            contentContainerStyle={{ paddingHorizontal: Default.fixPadding }}
          />
        </View>

        {/* Commented out Services section - moved to bottom navbar */}
        {/* <FlatList
          numColumns={3}
          data={servicesList}
          keyExtractor={(item) => item.key}
          renderItem={renderItemServices}
          showsVerticalScrollIndicator={false}
          columnWrapperStyle={{ paddingHorizontal: Default.fixPadding }}
          ListHeaderComponent={() => (
            <Text
              style={{
                textAlign: isRtl ? "right" : "left",
                ...Fonts.Bold18black,
                marginTop: Default.fixPadding * 1.5,
                marginHorizontal: Default.fixPadding * 2,
                marginBottom: Default.fixPadding,
              }}
            >
              {tr("services")}
            </Text>
          )}
          ListFooterComponent={<ListFooterComponent />}
        /> */}

        {/* Show only the business summary without services */}
        <ListFooterComponent />
      </View>
    </View>
  );
};

export default HomeScreen;

const styles = StyleSheet.create({
  performanceDashboard: {
    marginHorizontal: Default.fixPadding * 2,
    marginBottom: Default.fixPadding * 2,
    backgroundColor: Colors.white,
    borderRadius: 10,
    padding: Default.fixPadding * 1,
    ...Default.shadow,
  },
  performanceRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: Default.fixPadding * 1,
  },
  performanceBox: {
    flex: 1,
    alignItems: "center",
    marginHorizontal: Default.fixPadding * 0.5,
  },
  performanceTitle: {
    ...Fonts.Bold16black,
    marginBottom: Default.fixPadding * 0.5,
  },
  performanceValue: {
    ...Fonts.Bold20black,
    marginBottom: Default.fixPadding * 0.5,
  },
  performanceChange: {
    ...Fonts.SemiBold14grey,
    fontSize: 12,
  },
  performanceGauge: {
    flex: 1,
    alignItems: "center",
    marginHorizontal: Default.fixPadding * 0.5,
  },
  performanceGaugeValue: {
    ...Fonts.Bold20black,
    marginBottom: Default.fixPadding * 0.5,
  },
  performanceGaugeLabel: {
    ...Fonts.SemiBold14grey,
    marginBottom: Default.fixPadding * 0.5,
    fontSize: 12,
  },
  performanceGaugeLearnMore: {
    ...Fonts.SemiBold14primary,
  },
});