# ninja log v5
42	7500	****************	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	c45c475f37af6332
2	60	0	C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/CMakeFiles/cmake.verify_globs	902aa1ac5288afbb
175	11051	****************	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o	c7af203ec2a51d8f
27000	28258	****************	C:/Users/<USER>/Desktop/safaribank/android/app/build/intermediates/cxx/Debug/6c3h02c1/obj/arm64-v8a/libreact_codegen_rnpicker.so	439314b5a143d414
50	7853	****************	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	d27591215ed60624
105	10373	****************	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	8b7350538930586b
44800	57808	****************	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c571c1d6036a168ae4fc3473904c61ef/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	c7caec3cff28e3a5
42849	57462	****************	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0fecca949a576486bd8918ef885820d1/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	e9df69b956cb7d0b
93	10125	****************	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	70930de2ab327c70
59	11622	7748009997644309	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	d0666158fed399e
70	9861	7748009979413759	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	db803e04fff72cb5
81	11227	****************	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	e0165110022917bc
130	11818	****************	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o	e823883878bd730
27	81995	****************	CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/safaribank/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	9c16a18532ac3cbb
34	12793	****************	CMakeFiles/appmodules.dir/OnLoad.cpp.o	ec7fe8b77e980b18
35059	46027	****************	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4cfec5323944b6171731b6c48c986167/source/codegen/jni/safeareacontext-generated.cpp.o	a4d3809e0139758e
10126	26475	****************	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/4a6f3b899f2472db525b1aeaec1952cf/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o	e7d2f1fb271e75ed
46583	60909	****************	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c571c1d6036a168ae4fc3473904c61ef/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	6670f6de52c5aa2e
161	15434	****************	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o	d927977bfc79f736
147	15661	****************	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o	cd39e7ddd5cfd1e6
118	16320	7748010043933484	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o	7babc941ca634e34
11228	19863	7748010080043925	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/4a6f3b899f2472db525b1aeaec1952cf/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o	21bf8a1f9cd0791
11819	28104	7748010161018186	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o	f6aeea3b4f5dbeb9
66253	77230	7748010653046659	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	424fffae70e7aa98
32421	43220	7748010313464897	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	e82e5b0a32d67ba1
9862	20774	7748010088535974	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/ce0f8ac390a10c7078ceb651b2b5f869/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o	f19a942c49365a70
23167	39672	7748010276188081	pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ComponentDescriptors.cpp.o	4da276608f63eac1
28258	38410	7748010265505811	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	c1c47c05ad6f8307
24186	34610	7748010227397074	pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/pagerview-generated.cpp.o	696557771a0abb5
11053	20963	7748010090748632	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/64b15e53e28cb8271420dc093eb2729f/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o	2d0ba1b156367dd6
32330	43883	7748010320180780	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	30cacd5768b813a6
11623	24853	7748010129519983	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/ce0f8ac390a10c7078ceb651b2b5f869/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o	9a08492c48b7d9d7
24854	35748	7748010238264347	pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ShadowNodes.cpp.o	75bcda071e5c3eb8
15435	24024	7748010121527757	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	ecc7d0fdeeab89e6
7854	23166	7748010112596619	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o	686f4b93be8b9d62
7501	24185	7748010121772822	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o	fc607c90c0dc0360
15662	27506	7748010156380217	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	23ebb4e82e93dbfa
16320	28005	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	e3a3fa4d4768e31f
20776	37649	7748010257342117	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	2a1ff56858b5c8cb
10382	26999	7748010150240534	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/64b15e53e28cb8271420dc093eb2729f/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o	f01d3b389f648baf
12794	21034	7748010090348388	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	bc07078a1304b605
49293	58831	7748010469336440	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/070b71a12cd3933f88c5b6415f8a0dd2/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	ac37d9b1f57d5142
38411	52091	7748010401216748	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3c014a285d30952635ff25c24f3b628c/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	5ffbe8b180008a8e
64270	76133	7748010642075245	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/6f8bf51a5055904d319082ce2ec523d8/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o	52b2376eb341e73c
19864	32694	7748010206148387	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	4d342508754485f1
38160	50194	7748010383261440	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a12763d8e7192a1a1af2beaef787a338/components/safeareacontext/RNCSafeAreaViewState.cpp.o	2a6b785ba91269fe
20964	32330	7748010203591138	pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/EventEmitters.cpp.o	8bfa6180897e4dde
43884	56413	7748010444603502	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c84445bffdc9bdc8d818cf1df90d11cb/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	6f24b4d0f6ce3058
26476	35058	7748010231792568	pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/States.cpp.o	34e6d9e60c32151
46028	58167	7748010462471283	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8f3913b0681331b0df1ff2cddf0df947/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	2c1a7d9de05005b5
21035	32420	7748010205222460	pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/pagerviewJSI-generated.cpp.o	cfc8312600efc08c
24025	38160	7748010262210574	pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/Props.cpp.o	af29498b02bf1f44
35749	49292	7748010374049240	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d1928e7288675d66d0958ec2f22feae5/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	94f55b5c9dc1e3ec
28006	40297	7748010284093414	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	8369ed22c40850b4
39882	47551	7748010356858981	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/14f5e74951c6612c6104c971da74cdde/react/renderer/components/safeareacontext/States.cpp.o	6b537aa1ea68a7c8
34611	44799	7748010328898068	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2ed62cb3099698c2101e3d1e9852c7a9/safeareacontext/safeareacontextJSI-generated.cpp.o	2d8729ee5abc6a8a
37819	46582	7748010347194050	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f503560e2ebe3d68cd54b0b66c851b4b/renderer/components/safeareacontext/EventEmitters.cpp.o	b70623e86d860425
28105	36646	7748010247475660	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	d8fb2a0ba7385865
36647	53755	7748010418648753	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/89f4c1e374e38ed7d52e8efad6c07ed6/components/safeareacontext/ComponentDescriptors.cpp.o	f41ebdfb5f46fef0
43221	54875	7748010428643917	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c571c1d6036a168ae4fc3473904c61ef/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	67ed87af08a7a12a
37650	51587	7748010396656711	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/14f5e74951c6612c6104c971da74cdde/react/renderer/components/safeareacontext/Props.cpp.o	1d111867a925f9f
27507	37818	7748010258919257	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	ea0dbc05094104c
40298	53313	****************	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f503560e2ebe3d68cd54b0b66c851b4b/renderer/components/safeareacontext/ShadowNodes.cpp.o	bcacb7c113573468
32695	42849	****************	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	e00b7fb78008c3f9
53756	55200	****************	C:/Users/<USER>/Desktop/safaribank/android/app/build/intermediates/cxx/Debug/6c3h02c1/obj/arm64-v8a/libreact_codegen_safeareacontext.so	c21f3cb8c0ab259e
52092	66328	****************	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0981d4b7e38ed9fcd716e9fcafcebb91/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	b3273aba6f6c8511
47552	64269	****************	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/070b71a12cd3933f88c5b6415f8a0dd2/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	e953b74b18ddef42
51588	70841	****************	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0981d4b7e38ed9fcd716e9fcafcebb91/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	b637e3da8589de19
50195	61763	****************	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d4fd265a310a84ae642e041426a9da74/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	97dce1b8be6b1b2d
55099	70786	****************	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	d0bea19cf436abc6
55201	66304	7748010541291274	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/36bb9a9a578d4626246b7d56480f5ac3/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o	7bf860198326a7cc
61764	71810	7748010599058380	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/33e91c6f6354d3e633da9eb95b293770/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o	b4038326a441f127
56414	66060	7748010540124551	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/6f8bf51a5055904d319082ce2ec523d8/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o	63abbf5f2b38ef12
53314	77503	7748010655546126	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9e575de25c10bd66fa7a0876279bb99d/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	dce4d8324b0bea8
58833	76863	7748010649866349	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/6f8bf51a5055904d319082ce2ec523d8/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o	56c32d3e228071bb
66329	78927	****************	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	14b49497cd6b0f79
57463	74492	****************	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/36bb9a9a578d4626246b7d56480f5ac3/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o	2d324bd4ff94c57d
77504	79117	****************	C:/Users/<USER>/Desktop/safaribank/android/app/build/intermediates/cxx/Debug/6c3h02c1/obj/arm64-v8a/libreact_codegen_rnscreens.so	953fee956aaed1c
74493	86146	****************	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	c5901248726aa923
57861	76220	****************	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o	4cef1b978bf848d4
66305	78035	****************	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	8ec80ba25db55752
70865	82622	****************	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	290ba970129af59a
71811	81068	****************	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	fbd1f34955bf3e48
70786	82870	**********077581	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	9535aca23cb22e35
60910	82818	7748010709031666	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/33e91c6f6354d3e633da9eb95b293770/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o	66dae9be4dfd91e5
79118	86044	7748010741914952	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o	f40d9f79120e1cfb
76864	86560	7748010746934356	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o	a5aaedfa7c61b5f4
78036	87655	7748010758024671	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o	c03e1b356c19b1dd
78928	88769	7748010769034701	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o	ce5b6e1880234491
76221	88779	7748010769083900	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o	c2b2e16d93ae6301
76135	87971	7748010761119730	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o	f4851671f45479ad
77231	89398	****************	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o	d6d91833bd77aa18
58168	96287	****************	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/bdeaecfcbf9bb1797bbc30722cb9fe3c/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o	65858913eed2431f
96288	96532	****************	C:/Users/<USER>/Desktop/safaribank/android/app/build/intermediates/cxx/Debug/6c3h02c1/obj/arm64-v8a/libreact_codegen_rnsvg.so	42f9474bd7f3a7e
96532	96925	****************	C:/Users/<USER>/Desktop/safaribank/android/app/build/intermediates/cxx/Debug/6c3h02c1/obj/arm64-v8a/libappmodules.so	5d0ae72d1a5e06b9
0	68	0	clean	461098128ff045c1
60	1000	****************	build.ninja	76ddf500a02f7107
2	36	0	C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/arm64-v8a/CMakeFiles/cmake.verify_globs	902aa1ac5288afbb
50	4658	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	e3a3fa4d4768e31f
38	5583	****************	CMakeFiles/appmodules.dir/OnLoad.cpp.o	ec7fe8b77e980b18
29	32982	****************	CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/safaribank/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	9c16a18532ac3cbb
32983	33410	****************	C:/Users/<USER>/Desktop/safaribank/android/app/build/intermediates/cxx/Debug/6c3h02c1/obj/arm64-v8a/libappmodules.so	5d0ae72d1a5e06b9
