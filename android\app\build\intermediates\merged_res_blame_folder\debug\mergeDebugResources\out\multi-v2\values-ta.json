{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b7a298e4f795a18f3d41262269705afc\\transformed\\browser-1.6.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,274,381", "endColumns": "116,101,106,103", "endOffsets": "167,269,376,480"}, "to": {"startLines": "66,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6756,7331,7433,7540", "endColumns": "116,101,106,103", "endOffsets": "6868,7428,7535,7639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb5951a603c6ef86f8f4868bb3898655\\transformed\\react-android-0.76.9-debug\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,210,282,350,434,504,571,647,725,807,887,958,1040,1122,1200,1289,1379,1460,1532,1602,1696,1771,1854,1923", "endColumns": "74,79,71,67,83,69,66,75,77,81,79,70,81,81,77,88,89,80,71,69,93,74,82,68,77", "endOffsets": "125,205,277,345,429,499,566,642,720,802,882,953,1035,1117,1195,1284,1374,1455,1527,1597,1691,1766,1849,1918,1996"}, "to": {"startLines": "33,47,75,77,78,82,95,96,97,134,135,138,139,142,143,144,146,147,149,151,152,154,157,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3043,4440,7644,7784,7852,8189,9234,9301,9377,12454,12536,12784,12855,13109,13191,13269,13443,13533,13687,13830,13900,14095,14319,14522,14591", "endColumns": "74,79,71,67,83,69,66,75,77,81,79,70,81,81,77,88,89,80,71,69,93,74,82,68,77", "endOffsets": "3113,4515,7711,7847,7931,8254,9296,9372,9450,12531,12611,12850,12932,13186,13264,13353,13528,13609,13754,13895,13989,14165,14397,14586,14664"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0218db25d639d0bf937ef580de527e1b\\transformed\\material-1.6.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,311,420,538,622,686,794,862,923,1031,1117,1175,1259,1326,1380,1503,1565,1628,1682,1770,1898,1984,2066,2168,2248,2329,2418,2485,2551,2636,2724,2816,2885,2962,3042,3110,3209,3292,3384,3478,3552,3638,3732,3782,3848,3933,4020,4083,4148,4211,4319,4422,4520,4625", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,83,108,117,83,63,107,67,60,107,85,57,83,66,53,122,61,62,53,87,127,85,81,101,79,80,88,66,65,84,87,91,68,76,79,67,98,82,91,93,73,85,93,49,65,84,86,62,64,62,107,102,97,104,85", "endOffsets": "222,306,415,533,617,681,789,857,918,1026,1112,1170,1254,1321,1375,1498,1560,1623,1677,1765,1893,1979,2061,2163,2243,2324,2413,2480,2546,2631,2719,2811,2880,2957,3037,3105,3204,3287,3379,3473,3547,3633,3727,3777,3843,3928,4015,4078,4143,4206,4314,4417,4515,4620,4706"}, "to": {"startLines": "2,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3118,3948,4057,4175,7159,7223,7716,8128,8259,8367,8453,8511,8595,8662,8716,8839,8901,8964,9018,9106,9455,9541,9623,9725,9805,9886,9975,10042,10108,10193,10281,10373,10442,10519,10599,10667,10766,10849,10941,11035,11109,11195,11289,11339,11405,11490,11577,11640,11705,11768,11876,11979,12077,12368", "endLines": "5,34,42,43,44,70,71,76,81,83,84,85,86,87,88,89,90,91,92,93,94,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,133", "endColumns": "12,83,108,117,83,63,107,67,60,107,85,57,83,66,53,122,61,62,53,87,127,85,81,101,79,80,88,66,65,84,87,91,68,76,79,67,98,82,91,93,73,85,93,49,65,84,86,62,64,62,107,102,97,104,85", "endOffsets": "272,3197,4052,4170,4254,7218,7326,7779,8184,8362,8448,8506,8590,8657,8711,8834,8896,8959,9013,9101,9229,9536,9618,9720,9800,9881,9970,10037,10103,10188,10276,10368,10437,10514,10594,10662,10761,10844,10936,11030,11104,11190,11284,11334,11400,11485,11572,11635,11700,11763,11871,11974,12072,12177,12449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4b5fbf6908f3e082e261c13278e081e6\\transformed\\play-services-base-18.5.0\\res\\values-ta\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,667,815,939,1048,1145,1321,1424,1573,1704,1854,2006,2064,2123", "endColumns": "100,147,122,101,147,123,108,96,175,102,148,130,149,151,57,58,76", "endOffsets": "293,441,564,666,814,938,1047,1144,1320,1423,1572,1703,1853,2005,2063,2122,2199"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4520,4625,4777,4904,5010,5162,5290,5403,5665,5845,5952,6105,6240,6394,6550,6612,6675", "endColumns": "104,151,126,105,151,127,112,100,179,106,152,134,153,155,61,62,80", "endOffsets": "4620,4772,4899,5005,5157,5285,5398,5499,5840,5947,6100,6235,6389,6545,6607,6670,6751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\73718788808fe748888117607a7f7695\\transformed\\appcompat-1.7.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,390,492,607,696,807,928,1007,1083,1181,1281,1376,1470,1577,1677,1779,1873,1971,2069,2150,2258,2361,2460,2576,2679,2784,2941,12937", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "385,487,602,691,802,923,1002,1078,1176,1276,1371,1465,1572,1672,1774,1868,1966,2064,2145,2253,2356,2455,2571,2674,2779,2936,3038,13014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\960e1087eeccdf840de6823a1e7fc27f\\transformed\\ui-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,380,481,572,655,764,855,950,1032,1118,1208,1293,1366,1437,1517,1586", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,84,72,70,79,68,119", "endOffsets": "197,281,375,476,567,650,759,850,945,1027,1113,1203,1288,1361,1432,1512,1581,1701"}, "to": {"startLines": "45,46,67,68,69,79,80,131,132,136,137,141,145,148,150,155,156,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4259,4356,6873,6967,7068,7936,8019,12182,12273,12616,12698,13019,13358,13614,13759,14170,14250,14402", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,84,72,70,79,68,119", "endOffsets": "4351,4435,6962,7063,7154,8014,8123,12268,12363,12693,12779,13104,13438,13682,13825,14245,14314,14517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6f597dc2fa8134c9a491d7aedff8ce\\transformed\\foundation-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,148", "endColumns": "92,97", "endOffsets": "143,241"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "14669,14762", "endColumns": "92,97", "endOffsets": "14757,14855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1e754318e29fb6f6717a4ed39daeac16\\transformed\\play-services-basement-18.4.0\\res\\values-ta\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "156", "endOffsets": "351"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5504", "endColumns": "160", "endOffsets": "5660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a43c68922964e2a138ddfb9cf7287a27\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "35,36,37,38,39,40,41,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3202,3298,3401,3500,3598,3705,3820,13994", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "3293,3396,3495,3593,3700,3815,3943,14090"}}]}]}