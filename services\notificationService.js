import * as Notifications from 'expo-notifications';
import messaging from '@react-native-firebase/messaging';
import { Platform } from 'react-native';
import { router } from 'expo-router';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

class NotificationService {
  constructor() {
    this.fcmToken = null;
    this.notificationListener = null;
    this.responseListener = null;
  }

  async initialize() {
    try {
      console.log('🔔 Initializing Notification Service...');
      
      // Request permission
      await this.requestPermission();
      console.log('✅ Permissions granted');
      
      // Get FCM token
      await this.getFCMToken();
      
      // Set up listeners
      this.setupNotificationListeners();
      this.setupFCMListeners();
      
      console.log('🎉 Notification service initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize notification service:', error);
    }
  }

  async requestPermission() {
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#2e7d32',
      });
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      throw new Error('Permission not granted for notifications');
    }

    // Request FCM permission
    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (!enabled) {
      throw new Error('FCM permission not granted');
    }
  }

  async getFCMToken() {
    try {
      console.log('🔑 Getting FCM Token...');
      const token = await messaging().getToken();
      this.fcmToken = token;
      
      // Enhanced logging for easy copying
      console.log('\n' + '='.repeat(80));
      console.log('🎯 FCM TOKEN FOR TESTING:');
      console.log('='.repeat(80));
      console.log(token);
      console.log('='.repeat(80));
      console.log('📋 Copy this token to test push notifications from Firebase Console');
      console.log('='.repeat(80) + '\n');
      
      // Also save to a more accessible format
      if (__DEV__) {
        // In development, also log in a format easy to copy
        console.log('FCM_TOKEN_FOR_COPY:', token);
      }
      
      return token;
    } catch (error) {
      console.error('❌ Failed to get FCM token:', error);
      throw error;
    }
  }

  setupNotificationListeners() {
    console.log('👂 Setting up notification listeners...');
    
    // Handle notifications when app is in foreground
    this.notificationListener = Notifications.addNotificationReceivedListener(
      (notification) => {
        console.log('📱 Notification received in foreground:', notification);
        this.handleForegroundNotification(notification);
      }
    );

    // Handle notification tap
    this.responseListener = Notifications.addNotificationResponseReceivedListener(
      (response) => {
        console.log('👆 Notification tapped:', response);
        this.handleNotificationTap(response);
      }
    );
  }

  setupFCMListeners() {
    console.log('🔥 Setting up FCM listeners...');
    
    // Handle background messages
    messaging().setBackgroundMessageHandler(async (remoteMessage) => {
      console.log('📨 Message handled in the background!', remoteMessage);
    });

    // Handle foreground messages
    messaging().onMessage(async (remoteMessage) => {
      console.log('📬 FCM message received in foreground:', remoteMessage);
      
      // Show local notification
      await this.showLocalNotification(remoteMessage);
    });

    // Handle token refresh
    messaging().onTokenRefresh((token) => {
      console.log('🔄 FCM token refreshed:', token);
      console.log('\n' + '='.repeat(80));
      console.log('🆕 NEW FCM TOKEN:');
      console.log('='.repeat(80));
      console.log(token);
      console.log('='.repeat(80) + '\n');
      
      this.fcmToken = token;
    });
  }

  async showLocalNotification(remoteMessage) {
    await Notifications.scheduleNotificationAsync({
      content: {
        title: remoteMessage.notification?.title || 'SafariBank',
        body: remoteMessage.notification?.body || 'You have a new notification',
        data: remoteMessage.data || {},
        icon: './assets/images/notification-icon.png',
      },
      trigger: null, // Show immediately
    });
  }

  handleForegroundNotification(notification) {
    // Custom logic for handling foreground notifications
    console.log('🎯 Handling foreground notification:', notification.request.content.title);
  }

  handleNotificationTap(response) {
    // Handle navigation based on notification data
    const data = response.notification.request.content.data;

    console.log('🧭 Notification tap data:', data);

    if (data.screen) {
      console.log('📍 Navigating to:', data.screen);
      this.navigateToScreen(data.screen, data);
    }
  }

  // Navigation handler for different screens
  navigateToScreen(screenName, data) {
    console.log('🚀 Navigating to screen:', screenName, 'with data:', data);

    try {
      switch (screenName) {
        case 'home':
          router.push('/(tabs)/home/<USER>');
          break;

        case 'fundTransfer':
        case 'pay':
          router.push('/(tabs)/pay/payScreen');
          break;

        case 'statement':
        case 'statements':
          router.push('/(tabs)/deposit/depositScreen');
          break;

        case 'accountDetail':
          router.push('/accountDetail/accountDetailScreen');
          break;

        case 'latestTransaction':
          router.push('/latestTransaction/latestTransactionScreen');
          break;

        case 'notification':
          router.push('/notification/notificationScreen');
          break;

        case 'services':
          router.push('/services/servicesScreen');
          break;

        case 'statementDetail':
          router.push('/statement/statementScreen');
          break;

        case 'fundTransferDetail':
          router.push('/fundTransfer/fundTransferScreen');
          break;

        default:
          console.log('⚠️ Unknown screen:', screenName, '- opening home');
          router.push('/(tabs)/home/<USER>');
          break;
      }
    } catch (error) {
      console.error('❌ Navigation error:', error);
      // Fallback to home screen
      router.push('/(tabs)/home/<USER>');
    }
  }

  // Method to get current token (useful for debugging)
  getCurrentToken() {
    console.log('Current FCM Token:', this.fcmToken);
    return this.fcmToken;
  }

  async sendLocalNotification(title, body, data = {}) {
    console.log('📤 Sending local notification:', title);
    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
      },
      trigger: null,
    });
  }

  cleanup() {
    console.log('🧹 Cleaning up notification service...');
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
  }
}

export default new NotificationService();
