import {
    StyleSheet,
    Text,
    View,
    ScrollView,
    TouchableOpacity,
    TextInput,
    Dimensions,
    Switch,
} from "react-native";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Colors, Default, Fonts } from "../../constants/styles";
import Ionicons from "react-native-vector-icons/Ionicons";
import { useNavigation } from "expo-router";
import MyStatusBar from "../../components/myStatusBar";
import { Picker } from '@react-native-picker/picker';

const { width } = Dimensions.get("window");


const AddNewCard = () => {
    const navigation = useNavigation();
    const { t, i18n } = useTranslation();
    const isRtl = i18n.dir() == "rtl";

    function tr(key) {
        return t(`addNewCard:${key}`);
    }

    const [cardType, setCardType] = useState("physical");
    const [allocationType, setAllocationType] = useState("staff");
    const [staffID, setstaffID] = useState("");
    const [associatedPerson, setAssociatedPerson] = useState("");
    const [dailyLimit, setDailyLimit] = useState("");
    const [atmAccess, setAtmAccess] = useState(false);
    const [atmLimit, setAtmLimit] = useState("");
    const [internationalPayments, setInternationalPayments] = useState(false);
    const [locationRestrictions, setLocationRestrictions] = useState(false);
    const [deliveryAddress, setDeliveryAddress] = useState("");
    const [deliveryNotes, setDeliveryNotes] = useState("");

    return (
        <View style={{ flex: 1, backgroundColor: Colors.regularGrey }}>
            <MyStatusBar />
            <View
                style={{
                    flexDirection: isRtl ? "row-reverse" : "row",
                    alignItems: "center",
                    paddingVertical: Default.fixPadding * 1.2,
                    paddingHorizontal: Default.fixPadding * 2,
                    backgroundColor: Colors.regularGrey,
                    ...Default.shadow,
                }}
            >
                <TouchableOpacity onPress={() => navigation.pop()}>
                    <Ionicons
                        name={isRtl ? "arrow-forward" : "arrow-back"}
                        size={23}
                        color={Colors.black}
                    />
                </TouchableOpacity>
                <Text
                    style={{
                        ...Fonts.Bold20black,
                        marginHorizontal: Default.fixPadding * 1.5,
                    }}
                >
                    {tr("createNewCard")}
                </Text>
            </View>

            <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: Default.fixPadding * 8 }}
            >
                <View style={{ marginHorizontal: Default.fixPadding * 2 }}>
                    <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
                        {tr("cardType")}
                    </Text>
                    <View style={{ flexDirection: "row", marginTop: Default.fixPadding }}>
                        <TouchableOpacity
                            style={{
                                flex: 1,
                                padding: Default.fixPadding,
                                borderRadius: 10,
                                backgroundColor: cardType === "physical" ? Colors.primary : Colors.white,
                                alignItems: "center",
                                marginRight: Default.fixPadding * 0.5,
                                ...Default.shadow,
                            }}
                            onPress={() => setCardType("physical")}
                        >
                            <Text style={cardType === "physical" ? Fonts.SemiBold16white : Fonts.SemiBold16grey}>
                                {tr("physicalCard")}
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={{
                                flex: 1,
                                padding: Default.fixPadding,
                                borderRadius: 10,
                                backgroundColor: cardType === "virtual" ? Colors.primary : Colors.white,
                                alignItems: "center",
                                marginLeft: Default.fixPadding * 0.5,
                                ...Default.shadow,
                            }}
                            onPress={() => setCardType("virtual")}
                        >
                            <Text style={cardType === "virtual" ? Fonts.SemiBold16white : Fonts.SemiBold16grey}>
                                {tr("virtualCard")}
                            </Text>
                        </TouchableOpacity>
                    </View>

                    {cardType === "physical" && (
                        <Text style={{ ...Fonts.SemiBold14grey, marginTop: Default.fixPadding }}>
                            {tr("physicalCardNote")}
                        </Text>
                    )}

                    <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
                        {tr("allocationType")}
                    </Text>
                    <View style={{ marginTop: Default.fixPadding }}>
                        <Picker
                            selectedValue={allocationType}
                            onValueChange={(itemValue) => setAllocationType(itemValue)}
                            style={styles.picker}
                        >
                            <Picker.Item label={tr("allocateTostaff")} value="staff" />
                            <Picker.Item label={tr("createMyOwnCard")} value="own" />
                        </Picker>
                    </View>

                    {allocationType === "staff" && (
                        <>
                            <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
                                {tr("staffID")}
                            </Text>
                            <TextInput
                                value={staffID}
                                onChangeText={setstaffID}
                                placeholder={tr("enterstaffID")}
                                placeholderTextColor={Colors.grey}
                                style={styles.textInput}
                            />
                            <Text style={{ ...Fonts.SemiBold14grey, marginTop: Default.fixPadding }}>
                                {tr("associatedPerson")}: {associatedPerson}
                            </Text>
                        </>
                    )}

                    <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
                        {tr("dailyLimit")}
                    </Text>
                    <TextInput
                        value={dailyLimit}
                        onChangeText={setDailyLimit}
                        keyboardType="number-pad"
                        placeholder={tr("dailyLimitPlaceholder")}
                        placeholderTextColor={Colors.grey}
                        style={styles.textInput}
                    />

                    {cardType === "physical" && (
                        <>
                            <View style={{ flexDirection: "row", justifyContent: "space-between", marginTop: Default.fixPadding * 2 }}>
                                <Text style={{ ...Fonts.Bold17black }}>{tr("atmAccess")}</Text>
                                <Switch
                                    value={atmAccess}
                                    onValueChange={setAtmAccess}
                                    trackColor={{ false: Colors.lightGrey, true: Colors.primary }}
                                    thumbColor={atmAccess ? Colors.white : Colors.grey}
                                />
                            </View>

                            {atmAccess && (
                                <TextInput
                                    value={atmLimit}
                                    onChangeText={setAtmLimit}
                                    keyboardType="number-pad"
                                    placeholder={tr("atmLimitPlaceholder")}
                                    placeholderTextColor={Colors.grey}
                                    style={styles.textInput}
                                />
                            )}

                            <View style={{ flexDirection: "row", justifyContent: "space-between", marginTop: Default.fixPadding * 2 }}>
                                <Text style={{ ...Fonts.Bold17black }}>{tr("locationRestrictions")}</Text>
                                <Switch
                                    value={locationRestrictions}
                                    onValueChange={setLocationRestrictions}
                                    trackColor={{ false: Colors.lightGrey, true: Colors.primary }}
                                    thumbColor={locationRestrictions ? Colors.white : Colors.grey}
                                />
                            </View>

                            {locationRestrictions && (
                                <TextInput
                                    placeholder={tr("selectLocations")}
                                    placeholderTextColor={Colors.grey}
                                    style={styles.textInput}
                                />
                            )}

                            <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
                                {tr("deliveryAddress")}
                            </Text>
                            <TextInput
                                value={deliveryAddress}
                                onChangeText={setDeliveryAddress}
                                placeholder={tr("deliveryAddressPlaceholder")}
                                placeholderTextColor={Colors.grey}
                                style={styles.textInput}
                            />

                            <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
                                {tr("deliveryNotes")}
                            </Text>
                            <TextInput
                                value={deliveryNotes}
                                onChangeText={setDeliveryNotes}
                                placeholder={tr("deliveryNotesPlaceholder")}
                                placeholderTextColor={Colors.grey}
                                style={styles.textInput}
                            />
                        </>
                    )}

                    <View style={{ flexDirection: "row", justifyContent: "space-between", marginTop: Default.fixPadding * 2 }}>
                        <Text style={{ ...Fonts.Bold17black }}>{tr("internationalPayments")}</Text>
                        <Switch
                            value={internationalPayments}
                            onValueChange={setInternationalPayments}
                            trackColor={{ false: Colors.lightGrey, true: Colors.primary }}
                            thumbColor={internationalPayments ? Colors.white : Colors.grey}
                        />
                    </View>

                    <TouchableOpacity
                        onPress={() => {
                            // Handle card creation logic
                        }}
                        style={styles.createCardBtn}
                    >
                        <Text style={{ ...Fonts.Bold18white }}>{tr("createCard")}</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        onPress={() => navigation.pop()}
                        style={styles.cancelBtn}
                    >
                        <Text style={{ ...Fonts.Bold18black }}>{tr("cancel")}</Text>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </View>
    );
};

export default AddNewCard;

const styles = StyleSheet.create({
    textInput: {
        paddingVertical: Default.fixPadding * 1.2,
        paddingHorizontal: Default.fixPadding * 1.5,
        marginTop: Default.fixPadding * 1.2,
        borderRadius: 10,
        backgroundColor: Colors.white,
        ...Default.shadow,
    },
    createCardBtn: {
        justifyContent: "center",
        alignItems: "center",
        padding: Default.fixPadding * 1.2,
        marginVertical: Default.fixPadding * 2,
        borderRadius: 10,
        backgroundColor: Colors.primary,
        ...Default.shadowBtn,
    },
    cancelBtn: {
        justifyContent: "center",
        alignItems: "center",
        padding: Default.fixPadding * 1.2,
        marginBottom: Default.fixPadding * 2,
        borderRadius: 10,
        backgroundColor: Colors.lightGrey,
        ...Default.shadowBtn,
    },
    picker: {
        height: 50,
        width: '100%',
        backgroundColor: Colors.white,
        borderRadius: 10,
        ...Default.shadow,
    },
});