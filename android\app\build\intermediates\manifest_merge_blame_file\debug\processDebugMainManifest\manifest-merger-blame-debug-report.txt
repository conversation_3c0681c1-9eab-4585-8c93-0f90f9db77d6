1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.camelcase.rnstarbank"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:6:3-75
11-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:6:20-73
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:2:3-62
12-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:2:20-60
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:3:3-64
13-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:3:20-62
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:4:3-77
14-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:4:20-75
15    <uses-permission android:name="android.permission.RECORD_AUDIO" />
15-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:5:3-68
15-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:5:20-66
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:7:3-63
16-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:7:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:8:3-78
17-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:8:20-76
18
19    <queries>
19-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:9:3-15:13
20        <intent>
20-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:10:5-14:14
21            <action android:name="android.intent.action.VIEW" />
21-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:7-58
21-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:7-67
23-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:17-65
24
25            <data android:scheme="https" />
25-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
25-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:13-35
26        </intent>
27        <!-- Query open documents -->
28        <intent>
28-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
29            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
29-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
29-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
30        </intent>
31        <intent>
31-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-19:18
32
33            <!-- Required for picking images from the camera roll if targeting API 30 -->
34            <action android:name="android.media.action.IMAGE_CAPTURE" />
34-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-73
34-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:21-70
35        </intent>
36        <intent>
36-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-24:18
37
38            <!-- Required for picking images from the camera if targeting API 30 -->
39            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
39-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-80
39-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:21-77
40        </intent>
41        <intent>
41-->[:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
42
43            <!-- Required for opening tabs if targeting API 30 -->
44            <action android:name="android.support.customtabs.action.CustomTabsService" />
44-->[:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-90
44-->[:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-87
45        </intent> <!-- Needs to be explicitly declared on Android R+ -->
46        <package android:name="com.google.android.apps.maps" />
46-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
46-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
47
48        <intent>
48-->[com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:11:9-15:18
49            <action android:name="android.intent.action.VIEW" />
49-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:7-58
49-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:15-56
50
51            <data android:scheme="https" />
51-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
51-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:13-35
52        </intent>
53        <intent>
53-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
54            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
54-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
54-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
55        </intent>
56        <intent>
56-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
57            <action android:name="android.intent.action.GET_CONTENT" />
57-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
57-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
58
59            <category android:name="android.intent.category.OPENABLE" />
59-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:13-73
59-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:23-70
60
61            <data android:mimeType="*/*" />
61-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
62        </intent>
63    </queries>
64
65    <uses-permission android:name="android.permission.WAKE_LOCK" />
65-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
65-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
66    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
66-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
66-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
67    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
67-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
67-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
68    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
68-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
68-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
69
70    <uses-feature
70-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
71        android:glEsVersion="0x00020000"
71-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
72        android:required="true" /> <!-- Required by older versions of Google Play services to create IID tokens -->
72-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
73    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
73-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
73-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
74    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
74-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
74-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
75
76    <permission
76-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
77        android:name="com.camelcase.rnstarbank.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
77-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
78        android:protectionLevel="signature" />
78-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
79
80    <uses-permission android:name="com.camelcase.rnstarbank.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
80-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
80-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
81    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
81-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56e7624de93b82a8b7ebe7f674242512\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
81-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56e7624de93b82a8b7ebe7f674242512\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
82    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
83    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
84    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
85    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
86    <!-- for Samsung -->
87    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
87-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
87-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
88    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
88-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
88-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
89    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
90    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
91    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
92    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
93    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
94    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
95    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
96    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
97    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
98    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
98-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
98-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
99    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
99-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
99-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
100    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
100-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
100-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
101    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
101-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
101-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
102    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
102-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
102-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
103
104    <application
104-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:3-39:17
105        android:name="com.camelcase.rnstarbank.MainApplication"
105-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:16-47
106        android:allowBackup="true"
106-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:162-188
107        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
107-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
108        android:debuggable="true"
109        android:extractNativeLibs="false"
110        android:icon="@mipmap/ic_launcher"
110-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:81-115
111        android:label="@string/app_name"
111-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:48-80
112        android:roundIcon="@mipmap/ic_launcher_round"
112-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:116-161
113        android:supportsRtl="true"
113-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:221-247
114        android:theme="@style/AppTheme"
114-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:189-220
115        android:usesCleartextTraffic="true" >
115-->C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:6:18-53
116        <meta-data
116-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:17:5-103
117            android:name="com.google.android.geo.API_KEY"
117-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:17:16-61
118            android:value="GOOGLE_MAP_API_KEY_HERE" />
118-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:17:62-101
119        <meta-data
119-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:5-172
120            android:name="com.google.firebase.messaging.default_notification_color"
120-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:16-87
121            android:resource="@color/notification_icon_color" />
121-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:88-137
122        <meta-data
122-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:19:5-135
123            android:name="com.google.firebase.messaging.default_notification_icon"
123-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:19:16-86
124            android:resource="@drawable/notification_icon" />
124-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:19:87-133
125        <meta-data
125-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:20:5-136
126            android:name="expo.modules.notifications.default_notification_color"
126-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:20:16-84
127            android:resource="@color/notification_icon_color" />
127-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:20:85-134
128        <meta-data
128-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:5-132
129            android:name="expo.modules.notifications.default_notification_icon"
129-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:16-83
130            android:resource="@drawable/notification_icon" />
130-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:84-130
131        <meta-data
131-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:22:5-83
132            android:name="expo.modules.updates.ENABLED"
132-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:22:16-59
133            android:value="false" />
133-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:22:60-81
134        <meta-data
134-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:23:5-105
135            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
135-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:23:16-80
136            android:value="ALWAYS" />
136-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:23:81-103
137        <meta-data
137-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:24:5-99
138            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
138-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:24:16-79
139            android:value="0" />
139-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:24:80-97
140
141        <activity
141-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:5-37:16
142            android:name="com.camelcase.rnstarbank.MainActivity"
142-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:15-43
143            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
143-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:44-134
144            android:exported="true"
144-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:256-279
145            android:launchMode="singleTask"
145-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:135-166
146            android:screenOrientation="portrait"
146-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:280-316
147            android:theme="@style/Theme.App.SplashScreen"
147-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:210-255
148            android:windowSoftInputMode="adjustResize" >
148-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:167-209
149            <intent-filter>
149-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:26:7-29:23
150                <action android:name="android.intent.action.MAIN" />
150-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:27:9-60
150-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:27:17-58
151
152                <category android:name="android.intent.category.LAUNCHER" />
152-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:28:9-68
152-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:28:19-66
153            </intent-filter>
154            <intent-filter>
154-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:30:7-36:23
155                <action android:name="android.intent.action.VIEW" />
155-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:7-58
155-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:15-56
156
157                <category android:name="android.intent.category.DEFAULT" />
157-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:32:9-67
157-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:32:19-65
158                <category android:name="android.intent.category.BROWSABLE" />
158-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:7-67
158-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:17-65
159
160                <data android:scheme="myapp" />
160-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
160-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:13-35
161                <data android:scheme="com.camelcase.rnstarbank" />
161-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
161-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:13-35
162            </intent-filter>
163        </activity>
164
165        <uses-library
165-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:38:5-83
166            android:name="org.apache.http.legacy"
166-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:38:19-56
167            android:required="false" />
167-->C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:38:57-81
168
169        <provider
169-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
170            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
170-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
171            android:authorities="com.camelcase.rnstarbank.fileprovider"
171-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
172            android:exported="false"
172-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
173            android:grantUriPermissions="true" >
173-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
174            <meta-data
174-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
175                android:name="android.support.FILE_PROVIDER_PATHS"
175-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
176                android:resource="@xml/file_provider_paths" />
176-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
177        </provider>
178
179        <meta-data
179-->[:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:36
180            android:name="in_app_messaging_auto_collection_enabled"
180-->[:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-68
181            android:value="true" />
181-->[:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-33
182
183        <service
183-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:40
184            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService"
184-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-103
185            android:exported="false" />
185-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
186        <service
186-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:19
187            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService"
187-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-95
188            android:exported="false" >
188-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
189            <intent-filter>
189-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
190                <action android:name="com.google.firebase.MESSAGING_EVENT" />
190-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
190-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
191            </intent-filter>
192        </service>
193
194        <receiver
194-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-30:20
195            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver"
195-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-96
196            android:exported="true"
196-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
197            android:permission="com.google.android.c2dm.permission.SEND" >
197-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-73
198            <intent-filter>
198-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
199                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
199-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
199-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
200            </intent-filter>
201        </receiver>
202
203        <meta-data
203-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:37
204            android:name="delivery_metrics_exported_to_big_query_enabled"
204-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-74
205            android:value="false" />
205-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-34
206        <meta-data
206-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-37:36
207            android:name="firebase_messaging_auto_init_enabled"
207-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-64
208            android:value="true" />
208-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-33
209        <meta-data
209-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-40:37
210            android:name="firebase_messaging_notification_delegation_enabled"
210-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-78
211            android:value="false" />
211-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-34
212        <meta-data
212-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-43:32
213            android:name="com.google.firebase.messaging.default_notification_channel_id"
213-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-89
214            android:value="" />
214-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-29
215        <meta-data
215-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
216            android:name="app_data_collection_default_enabled"
216-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
217            android:value="true" />
217-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
218
219        <service
219-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
220            android:name="com.google.firebase.components.ComponentDiscoveryService"
220-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
221            android:directBootAware="true"
221-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
222            android:exported="false" >
222-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
223            <meta-data
223-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
224                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
224-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
225                android:value="com.google.firebase.components.ComponentRegistrar" />
225-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
226            <meta-data
226-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
227                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
227-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
228                android:value="com.google.firebase.components.ComponentRegistrar" />
228-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
229            <meta-data
229-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
230                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
230-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
231                android:value="com.google.firebase.components.ComponentRegistrar" />
231-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
232            <meta-data
232-->[com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:22:13-24:85
233                android:name="com.google.firebase.components:com.google.firebase.inappmessaging.display.FirebaseInAppMessagingDisplayKtxRegistrar"
233-->[com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:23:17-147
234                android:value="com.google.firebase.components.ComponentRegistrar" />
234-->[com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:24:17-82
235            <meta-data
235-->[com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:25:13-27:85
236                android:name="com.google.firebase.components:com.google.firebase.inappmessaging.display.FirebaseInAppMessagingDisplayRegistrar"
236-->[com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:26:17-144
237                android:value="com.google.firebase.components.ComponentRegistrar" />
237-->[com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:27:17-82
238            <meta-data
238-->[com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:14:13-16:85
239                android:name="com.google.firebase.components:com.google.firebase.inappmessaging.FirebaseInAppMessagingKtxRegistrar"
239-->[com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:15:17-132
240                android:value="com.google.firebase.components.ComponentRegistrar" />
240-->[com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:16:17-82
241            <meta-data
241-->[com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:17:13-19:85
242                android:name="com.google.firebase.components:com.google.firebase.inappmessaging.FirebaseInAppMessagingRegistrar"
242-->[com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:18:17-129
243                android:value="com.google.firebase.components.ComponentRegistrar" />
243-->[com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:19:17-82
244            <meta-data
244-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
245                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
245-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
246                android:value="com.google.firebase.components.ComponentRegistrar" />
246-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
247            <meta-data
247-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
248                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
248-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
249                android:value="com.google.firebase.components.ComponentRegistrar" />
249-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
250            <meta-data
250-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040e1442ba0e148f23a463c46367ee78\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
251                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
251-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040e1442ba0e148f23a463c46367ee78\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
252                android:value="com.google.firebase.components.ComponentRegistrar" />
252-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040e1442ba0e148f23a463c46367ee78\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
253            <meta-data
253-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
254                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
254-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
255                android:value="com.google.firebase.components.ComponentRegistrar" />
255-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
256            <meta-data
256-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2659dd4f697685d621dafb416377432a\transformed\firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
257                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
257-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2659dd4f697685d621dafb416377432a\transformed\firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
258                android:value="com.google.firebase.components.ComponentRegistrar" />
258-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2659dd4f697685d621dafb416377432a\transformed\firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
259            <meta-data
259-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4620ae1f56d345a5a969c47e5df67d9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
260                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
260-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4620ae1f56d345a5a969c47e5df67d9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
261                android:value="com.google.firebase.components.ComponentRegistrar" />
261-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4620ae1f56d345a5a969c47e5df67d9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
262        </service>
263
264        <provider
264-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
265            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
265-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
266            android:authorities="com.camelcase.rnstarbank.reactnativefirebaseappinitprovider"
266-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
267            android:exported="false"
267-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
268            android:initOrder="99" />
268-->[:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
269        <provider
269-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
270            android:name="expo.modules.filesystem.FileSystemFileProvider"
270-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
271            android:authorities="com.camelcase.rnstarbank.FileSystemFileProvider"
271-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
272            android:exported="false"
272-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
273            android:grantUriPermissions="true" >
273-->[:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
274            <meta-data
274-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
275                android:name="android.support.FILE_PROVIDER_PATHS"
275-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
276                android:resource="@xml/file_system_provider_paths" />
276-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
277        </provider>
278
279        <service
279-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:9-40:19
280            android:name="com.google.android.gms.metadata.ModuleDependencies"
280-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-78
281            android:enabled="false"
281-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-36
282            android:exported="false" >
282-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-37
283            <intent-filter>
283-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-35:29
284                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
284-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:17-94
284-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:25-91
285            </intent-filter>
286
287            <meta-data
287-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-39:36
288                android:name="photopicker_activity:0:required"
288-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-63
289                android:value="" />
289-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:17-33
290        </service>
291
292        <activity
292-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:9-44:59
293            android:name="com.canhub.cropper.CropImageActivity"
293-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-64
294            android:exported="true"
294-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
295            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
295-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-56
296        <provider
296-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:9-54:20
297            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
297-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:47:13-89
298            android:authorities="com.camelcase.rnstarbank.ImagePickerFileProvider"
298-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:48:13-75
299            android:exported="false"
299-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:49:13-37
300            android:grantUriPermissions="true" >
300-->[:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:50:13-47
301            <meta-data
301-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
302                android:name="android.support.FILE_PROVIDER_PATHS"
302-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
303                android:resource="@xml/image_picker_provider_paths" />
303-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
304        </provider>
305
306        <service
306-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-17:19
307            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
307-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-91
308            android:exported="false" >
308-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
309            <intent-filter android:priority="-1" >
309-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
310                <action android:name="com.google.firebase.MESSAGING_EVENT" />
310-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
310-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
311            </intent-filter>
312        </service>
313
314        <receiver
314-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:20
315            android:name="expo.modules.notifications.service.NotificationsService"
315-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-83
316            android:enabled="true"
316-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-35
317            android:exported="false" >
317-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
318            <intent-filter android:priority="-1" >
318-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-30:29
318-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:28-49
319                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
319-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:17-88
319-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:25-85
320                <action android:name="android.intent.action.BOOT_COMPLETED" />
320-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
320-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
321                <action android:name="android.intent.action.REBOOT" />
321-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-71
321-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:25-68
322                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
322-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-82
322-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-79
323                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
323-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
323-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
324                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
324-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
324-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
325            </intent-filter>
326        </receiver>
327
328        <activity
328-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-40:75
329            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
329-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-92
330            android:excludeFromRecents="true"
330-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-46
331            android:exported="false"
331-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-37
332            android:launchMode="standard"
332-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-42
333            android:noHistory="true"
333-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-37
334            android:taskAffinity=""
334-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-36
335            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
335-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-72
336
337        <meta-data
337-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
338            android:name="org.unimodules.core.AppLoader#react-native-headless"
338-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
339            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
339-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
340        <meta-data
340-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
341            android:name="com.facebook.soloader.enabled"
341-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
342            android:value="true" />
342-->[:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
343
344        <activity
344-->[com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb5951a603c6ef86f8f4868bb3898655\transformed\react-android-0.76.9-debug\AndroidManifest.xml:19:9-21:40
345            android:name="com.facebook.react.devsupport.DevSettingsActivity"
345-->[com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb5951a603c6ef86f8f4868bb3898655\transformed\react-android-0.76.9-debug\AndroidManifest.xml:20:13-77
346            android:exported="false" />
346-->[com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb5951a603c6ef86f8f4868bb3898655\transformed\react-android-0.76.9-debug\AndroidManifest.xml:21:13-37
347        <activity
347-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
348            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
348-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
349            android:excludeFromRecents="true"
349-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
350            android:exported="false"
350-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
351            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
351-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
352        <!--
353            Service handling Google Sign-In user revocation. For apps that do not integrate with
354            Google Sign-In, this service will never be started.
355        -->
356        <service
356-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
357            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
357-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
358            android:exported="true"
358-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
359            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
359-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
360            android:visibleToInstantApps="true" />
360-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
361
362        <meta-data
362-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2622c172769272601e11d1bc02c91d1e\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
363            android:name="com.google.android.gms.version"
363-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2622c172769272601e11d1bc02c91d1e\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
364            android:value="@integer/google_play_services_version" />
364-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2622c172769272601e11d1bc02c91d1e\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
365
366        <receiver
366-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
367            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
367-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
368            android:exported="true"
368-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
369            android:permission="com.google.android.c2dm.permission.SEND" >
369-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
370            <intent-filter>
370-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
371                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
371-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
371-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
372            </intent-filter>
373
374            <meta-data
374-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
375                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
375-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
376                android:value="true" />
376-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
377        </receiver>
378        <!--
379             FirebaseMessagingService performs security checks at runtime,
380             but set to not exported to explicitly avoid allowing another app to call it.
381        -->
382        <service
382-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
383            android:name="com.google.firebase.messaging.FirebaseMessagingService"
383-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
384            android:directBootAware="true"
384-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
385            android:exported="false" >
385-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
386            <intent-filter android:priority="-500" >
386-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
387                <action android:name="com.google.firebase.MESSAGING_EVENT" />
387-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
387-->[:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
388            </intent-filter>
389        </service>
390        <service
390-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
391            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
391-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
392            android:directBootAware="true"
392-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
393            android:exported="false" >
393-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
394            <meta-data
394-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
395                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
395-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
396                android:value="com.google.firebase.components.ComponentRegistrar" />
396-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
397            <meta-data
397-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0944679aabcfbd51c30d7f4ddb15c8b\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
398                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
398-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0944679aabcfbd51c30d7f4ddb15c8b\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
399                android:value="com.google.firebase.components.ComponentRegistrar" />
399-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0944679aabcfbd51c30d7f4ddb15c8b\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
400            <meta-data
400-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
401                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
401-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
402                android:value="com.google.firebase.components.ComponentRegistrar" />
402-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
403        </service>
404
405        <provider
405-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
406            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
406-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
407            android:authorities="com.camelcase.rnstarbank.mlkitinitprovider"
407-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
408            android:exported="false"
408-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
409            android:initOrder="99" />
409-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
410
411        <activity
411-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5fbf6908f3e082e261c13278e081e6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
412            android:name="com.google.android.gms.common.api.GoogleApiActivity"
412-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5fbf6908f3e082e261c13278e081e6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
413            android:exported="false"
413-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5fbf6908f3e082e261c13278e081e6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
414            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
414-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5fbf6908f3e082e261c13278e081e6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
415
416        <provider
416-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
417            android:name="com.google.firebase.provider.FirebaseInitProvider"
417-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
418            android:authorities="com.camelcase.rnstarbank.firebaseinitprovider"
418-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
419            android:directBootAware="true"
419-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
420            android:exported="false"
420-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
421            android:initOrder="100" />
421-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
422
423        <uses-library
423-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
424            android:name="androidx.camera.extensions.impl"
424-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
425            android:required="false" />
425-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
426
427        <service
427-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
428            android:name="androidx.camera.core.impl.MetadataHolderService"
428-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
429            android:enabled="false"
429-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
430            android:exported="false" >
430-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
431            <meta-data
431-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
432                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
432-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
433                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
433-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
434        </service>
435
436        <provider
436-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
437            android:name="com.canhub.cropper.CropFileProvider"
437-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
438            android:authorities="com.camelcase.rnstarbank.cropper.fileprovider"
438-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
439            android:exported="false"
439-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
440            android:grantUriPermissions="true" >
440-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
441            <meta-data
441-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
442                android:name="android.support.FILE_PROVIDER_PATHS"
442-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
443                android:resource="@xml/library_file_paths" />
443-->[:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
444        </provider>
445        <provider
445-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
446            android:name="androidx.startup.InitializationProvider"
446-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
447            android:authorities="com.camelcase.rnstarbank.androidx-startup"
447-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
448            android:exported="false" >
448-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
449            <meta-data
449-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
450                android:name="androidx.work.WorkManagerInitializer"
450-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
451                android:value="androidx.startup" />
451-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
452            <meta-data
452-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5032a21c15f221f6654cd901d0f3b5d7\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
453                android:name="androidx.emoji2.text.EmojiCompatInitializer"
453-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5032a21c15f221f6654cd901d0f3b5d7\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
454                android:value="androidx.startup" />
454-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5032a21c15f221f6654cd901d0f3b5d7\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
455            <meta-data
455-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\896c9732ce3c841382c5626265e136b7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
456                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
456-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\896c9732ce3c841382c5626265e136b7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
457                android:value="androidx.startup" />
457-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\896c9732ce3c841382c5626265e136b7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
458            <meta-data
458-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
459                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
459-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
460                android:value="androidx.startup" />
460-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
461        </provider>
462
463        <service
463-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
464            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
464-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
465            android:directBootAware="false"
465-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
466            android:enabled="@bool/enable_system_alarm_service_default"
466-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
467            android:exported="false" />
467-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
468        <service
468-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
469            android:name="androidx.work.impl.background.systemjob.SystemJobService"
469-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
470            android:directBootAware="false"
470-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
471            android:enabled="@bool/enable_system_job_service_default"
471-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
472            android:exported="true"
472-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
473            android:permission="android.permission.BIND_JOB_SERVICE" />
473-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
474        <service
474-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
475            android:name="androidx.work.impl.foreground.SystemForegroundService"
475-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
476            android:directBootAware="false"
476-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
477            android:enabled="@bool/enable_system_foreground_service_default"
477-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
478            android:exported="false" />
478-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
479
480        <receiver
480-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
481            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
481-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
482            android:directBootAware="false"
482-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
483            android:enabled="true"
483-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
484            android:exported="false" />
484-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
485        <receiver
485-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
486            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
486-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
487            android:directBootAware="false"
487-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
488            android:enabled="false"
488-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
489            android:exported="false" >
489-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
490            <intent-filter>
490-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
491                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
491-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
491-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
492                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
492-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
492-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
493            </intent-filter>
494        </receiver>
495        <receiver
495-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
496            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
496-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
497            android:directBootAware="false"
497-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
498            android:enabled="false"
498-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
499            android:exported="false" >
499-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
500            <intent-filter>
500-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
501                <action android:name="android.intent.action.BATTERY_OKAY" />
501-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
501-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
502                <action android:name="android.intent.action.BATTERY_LOW" />
502-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
502-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
503            </intent-filter>
504        </receiver>
505        <receiver
505-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
506            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
506-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
507            android:directBootAware="false"
507-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
508            android:enabled="false"
508-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
509            android:exported="false" >
509-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
510            <intent-filter>
510-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
511                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
511-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
511-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
512                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
512-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
512-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
513            </intent-filter>
514        </receiver>
515        <receiver
515-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
516            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
516-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
517            android:directBootAware="false"
517-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
518            android:enabled="false"
518-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
519            android:exported="false" >
519-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
520            <intent-filter>
520-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
521                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
521-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
521-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
522            </intent-filter>
523        </receiver>
524        <receiver
524-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
525            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
525-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
526            android:directBootAware="false"
526-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
527            android:enabled="false"
527-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
528            android:exported="false" >
528-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
529            <intent-filter>
529-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
530                <action android:name="android.intent.action.BOOT_COMPLETED" />
530-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
530-->[:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
531                <action android:name="android.intent.action.TIME_SET" />
531-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
531-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
532                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
532-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
532-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
533            </intent-filter>
534        </receiver>
535        <receiver
535-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
536            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
536-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
537            android:directBootAware="false"
537-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
538            android:enabled="@bool/enable_system_alarm_service_default"
538-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
539            android:exported="false" >
539-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
540            <intent-filter>
540-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
541                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
541-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
541-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
542            </intent-filter>
543        </receiver>
544        <receiver
544-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
545            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
545-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
546            android:directBootAware="false"
546-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
547            android:enabled="true"
547-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
548            android:exported="true"
548-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
549            android:permission="android.permission.DUMP" >
549-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
550            <intent-filter>
550-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
551                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
551-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
551-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
552            </intent-filter>
553        </receiver>
554        <receiver
554-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
555            android:name="androidx.profileinstaller.ProfileInstallReceiver"
555-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
556            android:directBootAware="false"
556-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
557            android:enabled="true"
557-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
558            android:exported="true"
558-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
559            android:permission="android.permission.DUMP" >
559-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
560            <intent-filter>
560-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
561                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
561-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
561-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
562            </intent-filter>
563            <intent-filter>
563-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
564                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
564-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
564-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
565            </intent-filter>
566            <intent-filter>
566-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
567                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
567-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
567-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
568            </intent-filter>
569            <intent-filter>
569-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
570                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
570-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
570-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
571            </intent-filter>
572        </receiver>
573
574        <service
574-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b49263e9abfea8d715f2aa7aa2f71a\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
575            android:name="androidx.room.MultiInstanceInvalidationService"
575-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b49263e9abfea8d715f2aa7aa2f71a\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
576            android:directBootAware="true"
576-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b49263e9abfea8d715f2aa7aa2f71a\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
577            android:exported="false" />
577-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b49263e9abfea8d715f2aa7aa2f71a\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
578        <service
578-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
579            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
579-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
580            android:exported="false" >
580-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
581            <meta-data
581-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
582                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
582-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
583                android:value="cct" />
583-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
584        </service>
585        <service
585-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
586            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
586-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
587            android:exported="false"
587-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
588            android:permission="android.permission.BIND_JOB_SERVICE" >
588-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
589        </service>
590
591        <receiver
591-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
592            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
592-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
593            android:exported="false" />
593-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
594    </application>
595
596</manifest>
