{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0218db25d639d0bf937ef580de527e1b\\transformed\\material-1.6.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,276,356,450,581,662,728,820,888,951,1054,1120,1176,1247,1307,1361,1473,1530,1591,1645,1721,1846,1932,2015,2123,2204,2287,2375,2442,2508,2582,2660,2749,2824,2900,2975,3046,3136,3209,3301,3397,3469,3545,3641,3694,3761,3848,3935,3997,4061,4124,4229,4333,4429,4536", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,79,93,130,80,65,91,67,62,102,65,55,70,59,53,111,56,60,53,75,124,85,82,107,80,82,87,66,65,73,77,88,74,75,74,70,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,79", "endOffsets": "271,351,445,576,657,723,815,883,946,1049,1115,1171,1242,1302,1356,1468,1525,1586,1640,1716,1841,1927,2010,2118,2199,2282,2370,2437,2503,2577,2655,2744,2819,2895,2970,3041,3131,3204,3296,3392,3464,3540,3636,3689,3756,3843,3930,3992,4056,4119,4224,4328,4424,4531,4611"}, "to": {"startLines": "2,35,43,44,45,71,72,77,82,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3128,3934,4028,4159,7072,7138,7614,8001,8131,8234,8300,8356,8427,8487,8541,8653,8710,8771,8825,8901,9258,9344,9427,9535,9616,9699,9787,9854,9920,9994,10072,10161,10236,10312,10387,10458,10548,10621,10713,10809,10881,10957,11053,11106,11173,11260,11347,11409,11473,11536,11641,11745,11841,12125", "endLines": "6,35,43,44,45,71,72,77,82,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,134", "endColumns": "12,79,93,130,80,65,91,67,62,102,65,55,70,59,53,111,56,60,53,75,124,85,82,107,80,82,87,66,65,73,77,88,74,75,74,70,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,79", "endOffsets": "321,3203,4023,4154,4235,7133,7225,7677,8059,8229,8295,8351,8422,8482,8536,8648,8705,8766,8820,8896,9021,9339,9422,9530,9611,9694,9782,9849,9915,9989,10067,10156,10231,10307,10382,10453,10543,10616,10708,10804,10876,10952,11048,11101,11168,11255,11342,11404,11468,11531,11636,11740,11836,11943,12200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4b5fbf6908f3e082e261c13278e081e6\\transformed\\play-services-base-18.5.0\\res\\values-sr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,447,569,675,825,948,1056,1154,1299,1402,1558,1681,1826,1964,2028,2089", "endColumns": "101,151,121,105,149,122,107,97,144,102,155,122,144,137,63,60,75", "endOffsets": "294,446,568,674,824,947,1055,1153,1298,1401,1557,1680,1825,1963,2027,2088,2164"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4507,4613,4769,4895,5005,5159,5286,5398,5630,5779,5886,6046,6173,6322,6464,6532,6597", "endColumns": "105,155,125,109,153,126,111,101,148,106,159,126,148,141,67,64,79", "endOffsets": "4608,4764,4890,5000,5154,5281,5393,5495,5774,5881,6041,6168,6317,6459,6527,6592,6672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\73718788808fe748888117607a7f7695\\transformed\\appcompat-1.7.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "326,433,534,640,726,830,952,1036,1117,1208,1301,1396,1490,1590,1683,1778,1883,1974,2065,2151,2256,2362,2465,2571,2680,2787,2957,12698", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "428,529,635,721,825,947,1031,1112,1203,1296,1391,1485,1585,1678,1773,1878,1969,2060,2146,2251,2357,2460,2566,2675,2782,2952,3049,12780"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\960e1087eeccdf840de6823a1e7fc27f\\transformed\\ui-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,998,1083,1156,1233,1311,1387,1466,1536", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,76,77,75,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,993,1078,1151,1228,1306,1382,1461,1531,1649"}, "to": {"startLines": "46,47,68,69,70,80,81,132,133,137,138,142,146,149,151,156,157,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4240,4337,6788,6885,6986,7833,7910,11948,12040,12376,12456,12785,13102,13337,13492,13904,13983,14132", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,76,77,75,78,69,117", "endOffsets": "4332,4419,6880,6981,7067,7905,7996,12035,12120,12451,12536,12853,13174,13410,13563,13978,14048,14245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6f597dc2fa8134c9a491d7aedff8ce\\transformed\\foundation-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,89", "endOffsets": "138,228"}, "to": {"startLines": "162,163", "startColumns": "4,4", "startOffsets": "14403,14491", "endColumns": "87,89", "endOffsets": "14486,14576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb5951a603c6ef86f8f4868bb3898655\\transformed\\react-android-0.76.9-debug\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,285,354,436,503,570,652,735,823,906,978,1063,1149,1225,1307,1389,1465,1542,1617,1705,1777,1856,1926", "endColumns": "73,82,72,68,81,66,66,81,82,87,82,71,84,85,75,81,81,75,76,74,87,71,78,69,82", "endOffsets": "124,207,280,349,431,498,565,647,730,818,901,973,1058,1144,1220,1302,1384,1460,1537,1612,1700,1772,1851,1921,2004"}, "to": {"startLines": "34,48,76,78,79,83,96,97,98,135,136,139,140,143,144,145,147,148,150,152,153,155,158,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3054,4424,7541,7682,7751,8064,9026,9093,9175,12205,12293,12541,12613,12858,12944,13020,13179,13261,13415,13568,13643,13832,14053,14250,14320", "endColumns": "73,82,72,68,81,66,66,81,82,87,82,71,84,85,75,81,81,75,76,74,87,71,78,69,82", "endOffsets": "3123,4502,7609,7746,7828,8126,9088,9170,9253,12288,12371,12608,12693,12939,13015,13097,13256,13332,13487,13638,13726,13899,14127,14315,14398"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1e754318e29fb6f6717a4ed39daeac16\\transformed\\play-services-basement-18.4.0\\res\\values-sr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5500", "endColumns": "129", "endOffsets": "5625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b7a298e4f795a18f3d41262269705afc\\transformed\\browser-1.6.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "67,73,74,75", "startColumns": "4,4,4,4", "startOffsets": "6677,7230,7330,7443", "endColumns": "110,99,112,97", "endOffsets": "6783,7325,7438,7536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a43c68922964e2a138ddfb9cf7287a27\\transformed\\core-1.13.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "36,37,38,39,40,41,42,154", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3208,3306,3408,3505,3609,3713,3818,13731", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3301,3403,3500,3604,3708,3813,3929,13827"}}]}]}