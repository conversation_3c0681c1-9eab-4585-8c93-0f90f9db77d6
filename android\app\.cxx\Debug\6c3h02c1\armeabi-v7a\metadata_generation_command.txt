                        -HC:\Users\<USER>\Desktop\safaribank\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.********
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.********
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.********\build\cmake\android.toolchain.cmake
-DCMA<PERSON>_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\safaribank\android\app\build\intermediates\cxx\Debug\6c3h02c1\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\safaribank\android\app\build\intermediates\cxx\Debug\6c3h02c1\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=C:\Users\<USER>\Desktop\safaribank\android\app\.cxx\Debug\6c3h02c1\prefab\armeabi-v7a\prefab
-BC:\Users\<USER>\Desktop\safaribank\android\app\.cxx\Debug\6c3h02c1\armeabi-v7a
-GNinja
-DPROJECT_BUILD_DIR=C:\Users\<USER>\Desktop\safaribank\android\app\build
-DREACT_ANDROID_DIR=C:\Users\<USER>\Desktop\safaribank\node_modules\react-native\ReactAndroid
-DANDROID_STL=c++_shared
-DANDROID_USE_LEGACY_TOOLCHAIN_FILE=ON
                        Build command args: []
                        Version: 2