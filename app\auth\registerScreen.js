import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  Image,
  TouchableOpacity,
  TextInput,
  Dimensions,
  Platform,
} from "react-native";
import { Colors, Fonts, Default } from "../../constants/styles";
import { useTranslation } from "react-i18next";
import Ionicons from "react-native-vector-icons/Ionicons";
import Feather from "react-native-vector-icons/Feather";
import Loader from "../../components/loader";
import MyStatusBar from "../../components/myStatusBar";
import { useNavigation } from "expo-router";
import { Picker } from '@react-native-picker/picker';
const { width, height } = Dimensions.get("window");

const RegisterScreen = () => {
  const navigation = useNavigation();

  const { t, i18n } = useTranslation();

  const isRtl = i18n.dir() == "rtl";

  function tr(key) {
    return t(`registerScreen:${key}`);
  }

  const [tin, setTin] = useState();
  const [entityPhone, setEntityPhone] = useState();
  const [personalPhone, setPersonalPhone] = useState();

  const [registerLoaderVisible, setRegisterLoaderVisible] = useState(false);

  const handleRegister = () => {
    setRegisterLoaderVisible(true);
    setTimeout(() => {
      setRegisterLoaderVisible(false);
      navigation.push("auth/registerValidate");
    }, 800);
  };

  const [registrationType, setRegistrationType] = useState('Entity');

  const organizationTypes = [
    "Registered Business",
    "Religious Organization",
    "NGO/Charity/Non-Profit",
    "Community-based Organization",
    "Machinga",
  ];

  return (
    <View style={styles.container}>
      <MyStatusBar />
      <View style={styles.header}>
        <Image
          source={require("../../assets/images/icon.png")}
          style={styles.icon}
        />
        <Text style={styles.headerText}>Register As a</Text>
        <View style={styles.radioContainer}>
          <TouchableOpacity
            style={styles.radioButton}
            onPress={() => setRegistrationType("Entity")}
          >
            <View
              style={
                registrationType === "Entity"
                  ? styles.radioCircle
                  : styles.radioCircleInactive
              }
            />
            <Text style={styles.radioText}>Entity</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.radioButton}
            onPress={() => setRegistrationType("Individual")}
          >
            <View
              style={
                registrationType === "Individual"
                  ? styles.radioCircle
                  : styles.radioCircleInactive
              }
            />
            <Text style={styles.radioText}>Individual</Text>
          </TouchableOpacity>
        </View>
      </View>
      {registrationType === "Entity" && (
        <View style={styles.form}>
          <Text>Select Organization Type</Text>
          <Picker
            selectedValue={registrationType}
            onValueChange={(itemValue) => setRegistrationType(itemValue)}
            style={styles.input}
          >
            {organizationTypes.map((type, index) => (
              <Picker.Item key={index} label={type} value={type} />
            ))}
          </Picker>
          {organizationTypes && (
            <>
              <TextInput
                value={tin}
                onChangeText={setTin}
                placeholder={`Enter ${organizationTypes} Registration Number`}
                placeholderTextColor={Colors.grey}
                style={styles.input}
              />
              {organizationTypes === "Registered Business" && (
                <TextInput
                  value={entityPhone}
                  onChangeText={setEntityPhone}
                  placeholder="Business License Number"
                  placeholderTextColor={Colors.grey}
                  style={styles.input}
                />
              )}
              <TextInput
                value={personalPhone}
                onChangeText={setPersonalPhone}
                placeholder="Your Phone Number"
                placeholderTextColor={Colors.grey}
                style={styles.input}
              />
              <TouchableOpacity
                onPress={handleRegister}
                style={styles.registerBtn}
              >
                <Text style={styles.registerBtnText}>Proceed</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      )}

      {registrationType === "Individual" && (
        <View style={styles.form}>
          <></>
          <>
            <TextInput
              value={tin}
              onChangeText={setTin}
              placeholder={`Enter NIDA Number`}
              placeholderTextColor={Colors.grey}
              style={styles.input}
            />

            <TextInput
              value={personalPhone}
              onChangeText={setPersonalPhone}
              placeholder="Your Phone Number"
              placeholderTextColor={Colors.grey}
              style={styles.input}
            />
            <TouchableOpacity
              onPress={handleRegister}
              style={styles.registerBtn}
            >
              <Text style={styles.registerBtnText}>Proceed</Text>
            </TouchableOpacity>
          </>

        </View>
      )}
      <View style={styles.footer}>
        <Image
          source={require("../../assets/images/splashIcon.png")}
          style={styles.footerIcon}
        />
        <Text style={styles.footerText}>SafariBank</Text>
      </View>
      <Loader visible={registerLoaderVisible} />
    </View>
  );
};

export default RegisterScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F5F5F5",
    padding: 20,
  },
  header: {
    alignItems: "center",
    marginBottom: 20,
  },
  icon: {
    width: 78,
    height: 78,
    marginBottom: 20,
  },
  headerText: {
    ...Fonts.Bold25black,
    marginBottom: 20,
  },
  radioContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 20,
  },
  radioButton: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 10,
  },
  radioCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: Colors.primary,
    marginRight: 10,
  },
  radioCircleInactive: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.grey,
    marginRight: 10,
  },
  radioText: {
    ...Fonts.SemiBold16black,
  },
  form: {
    backgroundColor: Colors.white,
    borderRadius: 10,
    padding: 20,
    ...Default.shadow,
  },
  input: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.grey,
    marginBottom: 20,
    padding: 10,
    ...Fonts.SemiBold16black,
  },
  registerBtn: {
    backgroundColor: Colors.primary,
    borderRadius: 10,
    padding: 15,
    alignItems: "center",
    marginTop: 20,
  },
  registerBtnText: {
    ...Fonts.Bold18white,
  },
  footer: {
    alignItems: "center",
    marginTop: 20,
  },
  footerIcon: {
    width: 50,
    height: 50,
    marginBottom: 10,
  },
  footerText: {
    ...Fonts.SemiBold16black,
  },
});