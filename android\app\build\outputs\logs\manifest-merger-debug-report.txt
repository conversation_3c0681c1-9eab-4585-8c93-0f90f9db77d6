-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:1:1-40:12
MERGED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:1:1-40:12
INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:expo] C:\Users\<USER>\Desktop\safaribank\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-pager-view] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-pager-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-13:12
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-49:12
MERGED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-30:12
MERGED from [:react-native-picker_picker] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-maps] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-application] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-application\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-asset] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-asset\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-blur] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-blur\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-camera] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:expo-constants] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-33:12
MERGED from [:expo-font] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-font\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-haptics] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:expo-image-loader] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-57:12
MERGED from [:expo-keep-awake] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-linking] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-43:12
MERGED from [:expo-splash-screen] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-system-ui] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-system-ui\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-18:12
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb5951a603c6ef86f8f4868bb3898655\transformed\react-android-0.76.9-debug\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2622c172769272601e11d1bc02c91d1e\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0218db25d639d0bf937ef580de527e1b\transformed\material-1.6.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dddd298b4d9cf70ba371b0dd2aa54613\transformed\recyclerview-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8054a8abed560bf50051308d630cfcc\transformed\viewpager2-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad0e965739122c625b6906e806fad7c2\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\090731bf989362adc93b988ceb343b62\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f210e85d3bbe882d7fad13a3aa2388ab\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5628928b1a224478c11719348483e091\transformed\play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd906157cff20d230fd972a33b605d29\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff97d695e9d6ce7f5d4c52d78fc1c76d\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0944679aabcfbd51c30d7f4ddb15c8b\transformed\vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5fbf6908f3e082e261c13278e081e6\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:2:1-31:12
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040e1442ba0e148f23a463c46367ee78\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8cececfee86f4c32846f18205362ae6\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\873559976d7f26baec02e4d65467cc23\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c094f7e8eab12c86cae9fd95d1a5816\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e56dfd0156b28fde4bc8f7aaf54aece4\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:14:1-22:12
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d086cf9355146b7efe734736376b37a0\transformed\camera-view-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a02cb9b14d89bb5fc638dece9e2f2d1\transformed\camera-video-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d676e438f7e88ea8ee000c319cdd8ce7\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3aea21cae1476c6763bf0c973e65b382\transformed\camera-core-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:2:1-38:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee6f597dc2fa8134c9a491d7aedff8ce\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b67aa6e48a58ed4f9c9a4739e0e6f811\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e56984a8a2f0c589681cba9092dfdaeb\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adfe9288035f76c1369e9e3a0c4e42df\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b30fea0c6acd45246c5ede9ee586743\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0707e6d21cdea09d9d3ba065f9a1443\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0e7fd7d2fbd60b92a42d1cdd9202d03\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6eefbfddb25fd8f1c1fc8d4ac968b4e9\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f581e06fecb51605f36caf0eb36c71e\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960e1087eeccdf840de6823a1e7fc27f\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c9a56deb5228aaa90f35875579420e0\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe06864431ca63a4ee07b668e6f5c3de\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2505f4384320b71a857524b70d4ce78\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfb456a01e33165c52fc8c2fd1494e11\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9decea98034819d325ef1a2dfb9f6f9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41b2e8700f2a500d616c94d47b8f1161\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b35cdb6c0d436743645f73ea8e12ceee\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e416f8cc7268e73ac6b4b306987a97a2\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da83cae8ab2750a88e69c2caa9f40d56\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73718788808fe748888117607a7f7695\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33269302ccbb23929f7e3719747c2997\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5032a21c15f221f6654cd901d0f3b5d7\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836d0933d3bca6f5b6b1b7812fa6def1\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a24bc841b308468633aac923f07fba3b\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3042085f0d0a8c8860c1808071a0be5\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79ddf078242ca0d214f5e32a72aaad96\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52ae79d65990ef6ec8fdc7f2582dcb48\transformed\webkit-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e27232b23c335b4f1247529100ab75a0\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abf56e644b2d413af36aaac11e2bfba3\transformed\animated-gif-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a629762cd21b21b3abb27c6bd182a925\transformed\webpsupport-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c10cb1a8cfbb4bf00952ae0180d17a4\transformed\fresco-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\591b9e19107cfec3a855e93bf7c4ea6c\transformed\imagepipeline-okhttp3-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07ecceafa0b21f4ad6fd93d3ba00b84e\transformed\animated-base-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e65aa7aa3748a6ac95eaa87e6bc03a07\transformed\animated-drawable-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85d5fc19afa94e9e76f439828ea3303c\transformed\vito-options-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7de2b47a3c4de9c22b2f27805e3135f4\transformed\drawee-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83c8d1798d121c13ecc9998bb3a41deb\transformed\nativeimagefilters-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83d70610f94b12dc6304783c91e5209\transformed\memory-type-native-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28ef0f9f73471c016bd38445eb2cd90b\transformed\memory-type-java-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ab01e3222e90becfd6e1d1cdbc40617\transformed\imagepipeline-native-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddce3a8ff357affca27215a59717a626\transformed\memory-type-ashmem-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fba25e05b382bbd9baff84211341f530\transformed\imagepipeline-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\210d682021ddf3c7324c87563932f8e7\transformed\nativeimagetranscoder-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\484ba6be08857343e8671308dcb6773b\transformed\imagepipeline-base-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e3181b02b3d159bc008e63c7b37dca\transformed\middleware-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7b4b4e68e673bf1e5dc70bd5a578a4f\transformed\ui-common-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\152b2fd2aaf6efcb2d88cb568448579e\transformed\soloader-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a196c3ad8d426d117071957f47af3d87\transformed\fbcore-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9275acb2b2b0558b98b8aa51577b07f7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e1b29b1680b83d0ebe0c9acee36a76d\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7a298e4f795a18f3d41262269705afc\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c1b6164e7cec16992ddd23c88884d02\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d85290e8303861192802a483e38fda60\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73bc231410ba9a836579d89e0d507fee\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30b361577d0dbf4d969483b813774f08\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02c2b11cc325721a49487f583d7d3e03\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60e52177505247937060cf7dc41401cd\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca054dbfd9c977c6d08a7ca4153f5500\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3304f062207815ce24807a0847c6a96e\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c137ef68211b4a84dd7b683525d38da\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fee943cec67546cb4818236d9e734fdb\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8d9bc4bc5e1b6c56bbc4f3d45d2a55a\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\670c9c0e93052a5c886fe1f0ee177b6d\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\305726b97d29bca95f2a9919106fc8cc\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd0582d724771f53c2b21439baaf2cce\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f89a6be60e33ffb95323a21753d712f\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5e83dca3c088a54cede7a3a7460cee7\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb9916e674b6606ead48b7e7b838ec5f\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15283da5e505f655373149df0b70856f\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fc680ee70ddb19b37f7af7af67dcee7\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35d0bba7488f8d3415bf1463eff56970\transformed\lifecycle-service-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\896c9732ce3c841382c5626265e136b7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d420bf24dfd22ae778b9c11b1ac34856\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\159f2a6a3f03b4c7799afbf3ac7ada7f\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15d0d2d50d5aacfdc54be73b75950dea\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\611f73073d401bc307977a28434f71ab\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a2273837b1be25121d119f83fea1801\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68178347d6819545f5eca6ffa8d5a495\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e7edfa7731eed1b9f8f9ca2722402fb\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\490c393581cebaab979cb288a3058887\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2659dd4f697685d621dafb416377432a\transformed\firebase-abt-21.1.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca9fe8dffaffd0276177f4c4dbe98628\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e754318e29fb6f6717a4ed39daeac16\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90a03f7809dd6a257e48f89620aebd83\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56667a5e21fa139cd3f12e2a30457f7b\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8947f1857e365b4dfc8d68df8e4505d1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cfaf9e3bcc37bb8684c7eca3bb881111\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3d2155b6a339a432691f2cc07d932de\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:vito-renderer:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30be7de32641cd84b015686741270929\transformed\vito-renderer-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e987ebe554e47d795a2f86bcc032caba\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1409c0ddab461f348a8775be723d6e1\transformed\hermes-android-0.76.9-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe21ee1e05caa973fe8a6cb87a2c4431\transformed\BlurView-version-2.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\307b84e16aa5a7dcd3baff8a5f987813\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b49263e9abfea8d715f2aa7aa2f71a\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\566e3d3c58e3511790fee7ac23280a5e\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4bdad2dbb77927c689cbd1c7268b350\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7785d5298779fda7920f72c8380c3fe\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\216ac7c3cf18ea9238c3318b1449c235\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8903c8be2c7b6d570e26cffa9781d60c\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4620ae1f56d345a5a969c47e5df67d9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4671f2d08a450ba3278a9a6e00897ad\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\916820b69b7418d0634b446e44a02d12\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f30f1a91dcc7351bc1eab4fe13a9a53\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.databinding:viewbinding:7.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23cb9d84685446cbbd0fb6247d8e64cc\transformed\viewbinding-7.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e357f8c25eb2e631fd1100691366cf58\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de98195958d21510ca90f7c5160e7135\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c1365ca63e5a7261477f4e0b6ed4a3\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d3549280e140fd34bc44e6b7365c4d2\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9add4af1225bf6e33cec5d8858afe35\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6605430dc729ba38acdccd1fb3bf2d44\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fbjni:fbjni:0.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\102477294d6543ddc195256a38a23f57\transformed\fbjni-0.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecb773877a9c128b02803682d93fa94e\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56e7624de93b82a8b7ebe7f674242512\transformed\installreferrer-2.2\AndroidManifest.xml:2:1-13:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:2:1-52:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20bbde8517f1f3fac91e36320987b7b2\transformed\protolite-well-known-types-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.reactivex.rxjava2:rxandroid:2.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbe614515554fd5a4e1a0f40ea6565d5\transformed\rxandroid-2.0.2\AndroidManifest.xml:15:1-21:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a66778682afc40b99c18ee68bef97b40\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:1:70-116
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:2:3-62
MERGED from [:expo-camera] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
MERGED from [:expo-camera] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-65
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:2:20-60
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:3:3-64
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\873559976d7f26baec02e4d65467cc23\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\873559976d7f26baec02e4d65467cc23\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:3:20-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:4:3-77
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-80
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:4:20-75
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:5:3-68
MERGED from [:expo-camera] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
MERGED from [:expo-camera] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:5:20-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:6:3-75
MERGED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:6:3-75
MERGED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:6:3-75
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb5951a603c6ef86f8f4868bb3898655\transformed\react-android-0.76.9-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb5951a603c6ef86f8f4868bb3898655\transformed\react-android-0.76.9-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:6:20-73
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:7:3-63
MERGED from [:expo-haptics] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:expo-haptics] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:7:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:8:3-78
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-81
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-81
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:8:20-76
queries
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:9:3-15:13
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-25:15
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-25:15
MERGED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
MERGED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:10:5-16:15
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:10:5-16:15
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:22:5-26:15
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:22:5-26:15
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:9:5-20:15
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:9:5-20:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:10:5-14:14
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:7-58
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:11:15-56
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:7-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:12:17-65
data
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:7-37
	android:scheme
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:13:13-35
application
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:3-39:17
MERGED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:3-39:17
MERGED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:3-39:17
INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:6:5-162
MERGED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:19
MERGED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:19
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-47:19
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-47:19
MERGED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-28:19
MERGED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-28:19
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:5-55:19
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:5-55:19
MERGED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-41:19
MERGED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-41:19
MERGED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb5951a603c6ef86f8f4868bb3898655\transformed\react-android-0.76.9-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb5951a603c6ef86f8f4868bb3898655\transformed\react-android-0.76.9-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2622c172769272601e11d1bc02c91d1e\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2622c172769272601e11d1bc02c91d1e\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0218db25d639d0bf937ef580de527e1b\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0218db25d639d0bf937ef580de527e1b\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad0e965739122c625b6906e806fad7c2\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad0e965739122c625b6906e806fad7c2\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5628928b1a224478c11719348483e091\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5628928b1a224478c11719348483e091\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0944679aabcfbd51c30d7f4ddb15c8b\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0944679aabcfbd51c30d7f4ddb15c8b\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5fbf6908f3e082e261c13278e081e6\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5fbf6908f3e082e261c13278e081e6\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:18:5-29:19
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:18:5-29:19
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:10:5-21:19
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:10:5-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040e1442ba0e148f23a463c46367ee78\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040e1442ba0e148f23a463c46367ee78\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8cececfee86f4c32846f18205362ae6\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8cececfee86f4c32846f18205362ae6\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3aea21cae1476c6763bf0c973e65b382\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3aea21cae1476c6763bf0c973e65b382\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:22:5-36:19
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:22:5-36:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2505f4384320b71a857524b70d4ce78\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2505f4384320b71a857524b70d4ce78\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b35cdb6c0d436743645f73ea8e12ceee\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b35cdb6c0d436743645f73ea8e12ceee\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5032a21c15f221f6654cd901d0f3b5d7\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5032a21c15f221f6654cd901d0f3b5d7\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\896c9732ce3c841382c5626265e136b7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\896c9732ce3c841382c5626265e136b7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\490c393581cebaab979cb288a3058887\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\490c393581cebaab979cb288a3058887\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2659dd4f697685d621dafb416377432a\transformed\firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2659dd4f697685d621dafb416377432a\transformed\firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca9fe8dffaffd0276177f4c4dbe98628\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca9fe8dffaffd0276177f4c4dbe98628\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e754318e29fb6f6717a4ed39daeac16\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e754318e29fb6f6717a4ed39daeac16\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8947f1857e365b4dfc8d68df8e4505d1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8947f1857e365b4dfc8d68df8e4505d1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe21ee1e05caa973fe8a6cb87a2c4431\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe21ee1e05caa973fe8a6cb87a2c4431\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b49263e9abfea8d715f2aa7aa2f71a\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b49263e9abfea8d715f2aa7aa2f71a\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4620ae1f56d345a5a969c47e5df67d9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4620ae1f56d345a5a969c47e5df67d9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de98195958d21510ca90f7c5160e7135\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de98195958d21510ca90f7c5160e7135\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecb773877a9c128b02803682d93fa94e\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecb773877a9c128b02803682d93fa94e\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56e7624de93b82a8b7ebe7f674242512\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56e7624de93b82a8b7ebe7f674242512\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a66778682afc40b99c18ee68bef97b40\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a66778682afc40b99c18ee68bef97b40\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:221-247
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:221-247
	android:label
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:48-80
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:48-80
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:116-161
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:116-161
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:6:54-74
	android:icon
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:81-115
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:81-115
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:162-188
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:162-188
	android:theme
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:189-220
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:189-220
	tools:replace
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml:6:18-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:16-47
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:16:16-47
meta-data#com.google.android.geo.API_KEY
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:17:5-103
	android:value
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:17:62-101
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:17:16-61
meta-data#com.google.firebase.messaging.default_notification_color
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:5-172
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:9-46:47
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:9-46:47
	android:resource
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:88-137
		REJECTED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:13-44
	tools:replace
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:138-170
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:18:16-87
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:19:5-135
	android:resource
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:19:87-133
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:19:16-86
meta-data#expo.modules.notifications.default_notification_color
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:20:5-136
	android:resource
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:20:85-134
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:20:16-84
meta-data#expo.modules.notifications.default_notification_icon
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:5-132
	android:resource
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:84-130
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:21:16-83
meta-data#expo.modules.updates.ENABLED
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:22:5-83
	android:value
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:22:60-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:22:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:23:5-105
	android:value
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:23:81-103
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:23:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:24:5-99
	android:value
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:24:80-97
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:24:16-79
activity#com.camelcase.rnstarbank.MainActivity
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:5-37:16
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:280-316
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:135-166
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:167-209
	android:exported
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:256-279
	android:configChanges
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:44-134
	android:theme
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:210-255
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:25:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:26:7-29:23
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:27:9-60
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:27:17-58
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:28:9-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:28:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:com.camelcase.rnstarbank+data:scheme:myapp
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:30:7-36:23
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:32:9-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:32:19-65
uses-library#org.apache.http.legacy
ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:38:5-83
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:38:57-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\safaribank\android\app\src\main\AndroidManifest.xml:38:19-56
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
MERGED from [:expo] C:\Users\<USER>\Desktop\safaribank\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\Users\<USER>\Desktop\safaribank\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-pager-view] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-pager-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-pager-view] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-pager-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-picker_picker] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-picker_picker] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-application] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-application\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-application] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-application\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-asset] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-asset\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-asset] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-asset\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-blur] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-blur\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-blur] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-blur\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-camera] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-camera] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-font] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-font\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-font] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-font\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-haptics] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-haptics] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-keep-awake] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-keep-awake] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-splash-screen] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-splash-screen] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-system-ui] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-system-ui\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-system-ui] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-system-ui\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb5951a603c6ef86f8f4868bb3898655\transformed\react-android-0.76.9-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb5951a603c6ef86f8f4868bb3898655\transformed\react-android-0.76.9-debug\AndroidManifest.xml:10:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2622c172769272601e11d1bc02c91d1e\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2622c172769272601e11d1bc02c91d1e\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0218db25d639d0bf937ef580de527e1b\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0218db25d639d0bf937ef580de527e1b\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dddd298b4d9cf70ba371b0dd2aa54613\transformed\recyclerview-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dddd298b4d9cf70ba371b0dd2aa54613\transformed\recyclerview-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8054a8abed560bf50051308d630cfcc\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8054a8abed560bf50051308d630cfcc\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad0e965739122c625b6906e806fad7c2\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad0e965739122c625b6906e806fad7c2\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\090731bf989362adc93b988ceb343b62\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\090731bf989362adc93b988ceb343b62\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f210e85d3bbe882d7fad13a3aa2388ab\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f210e85d3bbe882d7fad13a3aa2388ab\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5628928b1a224478c11719348483e091\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5628928b1a224478c11719348483e091\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd906157cff20d230fd972a33b605d29\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd906157cff20d230fd972a33b605d29\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff97d695e9d6ce7f5d4c52d78fc1c76d\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff97d695e9d6ce7f5d4c52d78fc1c76d\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0944679aabcfbd51c30d7f4ddb15c8b\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0944679aabcfbd51c30d7f4ddb15c8b\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5fbf6908f3e082e261c13278e081e6\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5fbf6908f3e082e261c13278e081e6\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040e1442ba0e148f23a463c46367ee78\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040e1442ba0e148f23a463c46367ee78\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8cececfee86f4c32846f18205362ae6\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8cececfee86f4c32846f18205362ae6\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\873559976d7f26baec02e4d65467cc23\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\873559976d7f26baec02e4d65467cc23\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c094f7e8eab12c86cae9fd95d1a5816\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c094f7e8eab12c86cae9fd95d1a5816\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e56dfd0156b28fde4bc8f7aaf54aece4\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:18:5-20:70
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e56dfd0156b28fde4bc8f7aaf54aece4\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:18:5-20:70
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d086cf9355146b7efe734736376b37a0\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d086cf9355146b7efe734736376b37a0\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a02cb9b14d89bb5fc638dece9e2f2d1\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a02cb9b14d89bb5fc638dece9e2f2d1\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d676e438f7e88ea8ee000c319cdd8ce7\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d676e438f7e88ea8ee000c319cdd8ce7\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3aea21cae1476c6763bf0c973e65b382\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3aea21cae1476c6763bf0c973e65b382\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee6f597dc2fa8134c9a491d7aedff8ce\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee6f597dc2fa8134c9a491d7aedff8ce\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b67aa6e48a58ed4f9c9a4739e0e6f811\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b67aa6e48a58ed4f9c9a4739e0e6f811\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e56984a8a2f0c589681cba9092dfdaeb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e56984a8a2f0c589681cba9092dfdaeb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adfe9288035f76c1369e9e3a0c4e42df\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adfe9288035f76c1369e9e3a0c4e42df\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b30fea0c6acd45246c5ede9ee586743\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b30fea0c6acd45246c5ede9ee586743\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0707e6d21cdea09d9d3ba065f9a1443\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0707e6d21cdea09d9d3ba065f9a1443\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0e7fd7d2fbd60b92a42d1cdd9202d03\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0e7fd7d2fbd60b92a42d1cdd9202d03\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6eefbfddb25fd8f1c1fc8d4ac968b4e9\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6eefbfddb25fd8f1c1fc8d4ac968b4e9\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f581e06fecb51605f36caf0eb36c71e\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f581e06fecb51605f36caf0eb36c71e\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960e1087eeccdf840de6823a1e7fc27f\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960e1087eeccdf840de6823a1e7fc27f\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c9a56deb5228aaa90f35875579420e0\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c9a56deb5228aaa90f35875579420e0\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe06864431ca63a4ee07b668e6f5c3de\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe06864431ca63a4ee07b668e6f5c3de\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2505f4384320b71a857524b70d4ce78\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2505f4384320b71a857524b70d4ce78\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfb456a01e33165c52fc8c2fd1494e11\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfb456a01e33165c52fc8c2fd1494e11\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9decea98034819d325ef1a2dfb9f6f9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9decea98034819d325ef1a2dfb9f6f9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41b2e8700f2a500d616c94d47b8f1161\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41b2e8700f2a500d616c94d47b8f1161\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b35cdb6c0d436743645f73ea8e12ceee\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b35cdb6c0d436743645f73ea8e12ceee\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e416f8cc7268e73ac6b4b306987a97a2\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e416f8cc7268e73ac6b4b306987a97a2\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da83cae8ab2750a88e69c2caa9f40d56\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da83cae8ab2750a88e69c2caa9f40d56\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73718788808fe748888117607a7f7695\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73718788808fe748888117607a7f7695\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33269302ccbb23929f7e3719747c2997\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33269302ccbb23929f7e3719747c2997\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5032a21c15f221f6654cd901d0f3b5d7\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5032a21c15f221f6654cd901d0f3b5d7\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836d0933d3bca6f5b6b1b7812fa6def1\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836d0933d3bca6f5b6b1b7812fa6def1\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a24bc841b308468633aac923f07fba3b\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a24bc841b308468633aac923f07fba3b\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3042085f0d0a8c8860c1808071a0be5\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3042085f0d0a8c8860c1808071a0be5\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79ddf078242ca0d214f5e32a72aaad96\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79ddf078242ca0d214f5e32a72aaad96\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52ae79d65990ef6ec8fdc7f2582dcb48\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52ae79d65990ef6ec8fdc7f2582dcb48\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e27232b23c335b4f1247529100ab75a0\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e27232b23c335b4f1247529100ab75a0\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abf56e644b2d413af36aaac11e2bfba3\transformed\animated-gif-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abf56e644b2d413af36aaac11e2bfba3\transformed\animated-gif-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a629762cd21b21b3abb27c6bd182a925\transformed\webpsupport-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a629762cd21b21b3abb27c6bd182a925\transformed\webpsupport-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c10cb1a8cfbb4bf00952ae0180d17a4\transformed\fresco-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c10cb1a8cfbb4bf00952ae0180d17a4\transformed\fresco-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\591b9e19107cfec3a855e93bf7c4ea6c\transformed\imagepipeline-okhttp3-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\591b9e19107cfec3a855e93bf7c4ea6c\transformed\imagepipeline-okhttp3-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07ecceafa0b21f4ad6fd93d3ba00b84e\transformed\animated-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07ecceafa0b21f4ad6fd93d3ba00b84e\transformed\animated-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e65aa7aa3748a6ac95eaa87e6bc03a07\transformed\animated-drawable-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e65aa7aa3748a6ac95eaa87e6bc03a07\transformed\animated-drawable-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85d5fc19afa94e9e76f439828ea3303c\transformed\vito-options-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85d5fc19afa94e9e76f439828ea3303c\transformed\vito-options-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7de2b47a3c4de9c22b2f27805e3135f4\transformed\drawee-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7de2b47a3c4de9c22b2f27805e3135f4\transformed\drawee-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83c8d1798d121c13ecc9998bb3a41deb\transformed\nativeimagefilters-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83c8d1798d121c13ecc9998bb3a41deb\transformed\nativeimagefilters-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83d70610f94b12dc6304783c91e5209\transformed\memory-type-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83d70610f94b12dc6304783c91e5209\transformed\memory-type-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28ef0f9f73471c016bd38445eb2cd90b\transformed\memory-type-java-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28ef0f9f73471c016bd38445eb2cd90b\transformed\memory-type-java-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ab01e3222e90becfd6e1d1cdbc40617\transformed\imagepipeline-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ab01e3222e90becfd6e1d1cdbc40617\transformed\imagepipeline-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddce3a8ff357affca27215a59717a626\transformed\memory-type-ashmem-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddce3a8ff357affca27215a59717a626\transformed\memory-type-ashmem-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fba25e05b382bbd9baff84211341f530\transformed\imagepipeline-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fba25e05b382bbd9baff84211341f530\transformed\imagepipeline-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\210d682021ddf3c7324c87563932f8e7\transformed\nativeimagetranscoder-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\210d682021ddf3c7324c87563932f8e7\transformed\nativeimagetranscoder-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\484ba6be08857343e8671308dcb6773b\transformed\imagepipeline-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\484ba6be08857343e8671308dcb6773b\transformed\imagepipeline-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e3181b02b3d159bc008e63c7b37dca\transformed\middleware-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e3181b02b3d159bc008e63c7b37dca\transformed\middleware-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7b4b4e68e673bf1e5dc70bd5a578a4f\transformed\ui-common-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7b4b4e68e673bf1e5dc70bd5a578a4f\transformed\ui-common-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\152b2fd2aaf6efcb2d88cb568448579e\transformed\soloader-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\152b2fd2aaf6efcb2d88cb568448579e\transformed\soloader-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a196c3ad8d426d117071957f47af3d87\transformed\fbcore-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a196c3ad8d426d117071957f47af3d87\transformed\fbcore-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9275acb2b2b0558b98b8aa51577b07f7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9275acb2b2b0558b98b8aa51577b07f7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e1b29b1680b83d0ebe0c9acee36a76d\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e1b29b1680b83d0ebe0c9acee36a76d\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7a298e4f795a18f3d41262269705afc\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7a298e4f795a18f3d41262269705afc\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c1b6164e7cec16992ddd23c88884d02\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c1b6164e7cec16992ddd23c88884d02\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d85290e8303861192802a483e38fda60\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d85290e8303861192802a483e38fda60\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73bc231410ba9a836579d89e0d507fee\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73bc231410ba9a836579d89e0d507fee\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30b361577d0dbf4d969483b813774f08\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30b361577d0dbf4d969483b813774f08\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02c2b11cc325721a49487f583d7d3e03\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02c2b11cc325721a49487f583d7d3e03\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60e52177505247937060cf7dc41401cd\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60e52177505247937060cf7dc41401cd\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca054dbfd9c977c6d08a7ca4153f5500\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca054dbfd9c977c6d08a7ca4153f5500\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3304f062207815ce24807a0847c6a96e\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3304f062207815ce24807a0847c6a96e\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c137ef68211b4a84dd7b683525d38da\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c137ef68211b4a84dd7b683525d38da\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fee943cec67546cb4818236d9e734fdb\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fee943cec67546cb4818236d9e734fdb\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8d9bc4bc5e1b6c56bbc4f3d45d2a55a\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8d9bc4bc5e1b6c56bbc4f3d45d2a55a\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\670c9c0e93052a5c886fe1f0ee177b6d\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\670c9c0e93052a5c886fe1f0ee177b6d\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\305726b97d29bca95f2a9919106fc8cc\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\305726b97d29bca95f2a9919106fc8cc\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd0582d724771f53c2b21439baaf2cce\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd0582d724771f53c2b21439baaf2cce\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f89a6be60e33ffb95323a21753d712f\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f89a6be60e33ffb95323a21753d712f\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5e83dca3c088a54cede7a3a7460cee7\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5e83dca3c088a54cede7a3a7460cee7\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb9916e674b6606ead48b7e7b838ec5f\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb9916e674b6606ead48b7e7b838ec5f\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15283da5e505f655373149df0b70856f\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15283da5e505f655373149df0b70856f\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fc680ee70ddb19b37f7af7af67dcee7\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fc680ee70ddb19b37f7af7af67dcee7\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35d0bba7488f8d3415bf1463eff56970\transformed\lifecycle-service-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35d0bba7488f8d3415bf1463eff56970\transformed\lifecycle-service-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\896c9732ce3c841382c5626265e136b7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\896c9732ce3c841382c5626265e136b7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d420bf24dfd22ae778b9c11b1ac34856\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d420bf24dfd22ae778b9c11b1ac34856\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\159f2a6a3f03b4c7799afbf3ac7ada7f\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\159f2a6a3f03b4c7799afbf3ac7ada7f\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15d0d2d50d5aacfdc54be73b75950dea\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15d0d2d50d5aacfdc54be73b75950dea\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\611f73073d401bc307977a28434f71ab\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\611f73073d401bc307977a28434f71ab\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a2273837b1be25121d119f83fea1801\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a2273837b1be25121d119f83fea1801\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68178347d6819545f5eca6ffa8d5a495\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68178347d6819545f5eca6ffa8d5a495\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e7edfa7731eed1b9f8f9ca2722402fb\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e7edfa7731eed1b9f8f9ca2722402fb\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\490c393581cebaab979cb288a3058887\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\490c393581cebaab979cb288a3058887\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2659dd4f697685d621dafb416377432a\transformed\firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2659dd4f697685d621dafb416377432a\transformed\firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca9fe8dffaffd0276177f4c4dbe98628\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca9fe8dffaffd0276177f4c4dbe98628\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e754318e29fb6f6717a4ed39daeac16\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e754318e29fb6f6717a4ed39daeac16\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90a03f7809dd6a257e48f89620aebd83\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90a03f7809dd6a257e48f89620aebd83\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56667a5e21fa139cd3f12e2a30457f7b\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56667a5e21fa139cd3f12e2a30457f7b\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8947f1857e365b4dfc8d68df8e4505d1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8947f1857e365b4dfc8d68df8e4505d1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cfaf9e3bcc37bb8684c7eca3bb881111\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cfaf9e3bcc37bb8684c7eca3bb881111\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3d2155b6a339a432691f2cc07d932de\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3d2155b6a339a432691f2cc07d932de\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30be7de32641cd84b015686741270929\transformed\vito-renderer-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30be7de32641cd84b015686741270929\transformed\vito-renderer-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e987ebe554e47d795a2f86bcc032caba\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e987ebe554e47d795a2f86bcc032caba\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1409c0ddab461f348a8775be723d6e1\transformed\hermes-android-0.76.9-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1409c0ddab461f348a8775be723d6e1\transformed\hermes-android-0.76.9-debug\AndroidManifest.xml:5:5-44
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe21ee1e05caa973fe8a6cb87a2c4431\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe21ee1e05caa973fe8a6cb87a2c4431\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\307b84e16aa5a7dcd3baff8a5f987813\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\307b84e16aa5a7dcd3baff8a5f987813\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b49263e9abfea8d715f2aa7aa2f71a\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b49263e9abfea8d715f2aa7aa2f71a\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\566e3d3c58e3511790fee7ac23280a5e\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\566e3d3c58e3511790fee7ac23280a5e\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4bdad2dbb77927c689cbd1c7268b350\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4bdad2dbb77927c689cbd1c7268b350\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7785d5298779fda7920f72c8380c3fe\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7785d5298779fda7920f72c8380c3fe\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\216ac7c3cf18ea9238c3318b1449c235\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\216ac7c3cf18ea9238c3318b1449c235\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8903c8be2c7b6d570e26cffa9781d60c\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8903c8be2c7b6d570e26cffa9781d60c\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4620ae1f56d345a5a969c47e5df67d9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4620ae1f56d345a5a969c47e5df67d9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4671f2d08a450ba3278a9a6e00897ad\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4671f2d08a450ba3278a9a6e00897ad\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\916820b69b7418d0634b446e44a02d12\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\916820b69b7418d0634b446e44a02d12\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f30f1a91dcc7351bc1eab4fe13a9a53\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f30f1a91dcc7351bc1eab4fe13a9a53\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.databinding:viewbinding:7.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23cb9d84685446cbbd0fb6247d8e64cc\transformed\viewbinding-7.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.databinding:viewbinding:7.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23cb9d84685446cbbd0fb6247d8e64cc\transformed\viewbinding-7.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e357f8c25eb2e631fd1100691366cf58\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e357f8c25eb2e631fd1100691366cf58\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de98195958d21510ca90f7c5160e7135\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de98195958d21510ca90f7c5160e7135\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c1365ca63e5a7261477f4e0b6ed4a3\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c1365ca63e5a7261477f4e0b6ed4a3\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d3549280e140fd34bc44e6b7365c4d2\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d3549280e140fd34bc44e6b7365c4d2\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9add4af1225bf6e33cec5d8858afe35\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9add4af1225bf6e33cec5d8858afe35\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6605430dc729ba38acdccd1fb3bf2d44\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6605430dc729ba38acdccd1fb3bf2d44\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fbjni:fbjni:0.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\102477294d6543ddc195256a38a23f57\transformed\fbjni-0.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\102477294d6543ddc195256a38a23f57\transformed\fbjni-0.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecb773877a9c128b02803682d93fa94e\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecb773877a9c128b02803682d93fa94e\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56e7624de93b82a8b7ebe7f674242512\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56e7624de93b82a8b7ebe7f674242512\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20bbde8517f1f3fac91e36320987b7b2\transformed\protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20bbde8517f1f3fac91e36320987b7b2\transformed\protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [io.reactivex.rxjava2:rxandroid:2.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbe614515554fd5a4e1a0f40ea6565d5\transformed\rxandroid-2.0.2\AndroidManifest.xml:19:5-43
MERGED from [io.reactivex.rxjava2:rxandroid:2.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbe614515554fd5a4e1a0f40ea6565d5\transformed\rxandroid-2.0.2\AndroidManifest.xml:19:5-43
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a66778682afc40b99c18ee68bef97b40\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a66778682afc40b99c18ee68bef97b40\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	tools:overrideLibrary
		ADDED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e56dfd0156b28fde4bc8f7aaf54aece4\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:20:9-67
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\AndroidManifest.xml
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
	android:exported
		ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
	android:resource
		ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
meta-data#in_app_messaging_auto_collection_enabled
ADDED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:36
	android:value
		ADDED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-33
	android:name
		ADDED from [:react-native-firebase_in-app-messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-68
uses-permission#android.permission.WAKE_LOCK
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\873559976d7f26baec02e4d65467cc23\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\873559976d7f26baec02e4d65467cc23\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\873559976d7f26baec02e4d65467cc23\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\873559976d7f26baec02e4d65467cc23\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
service#io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:40
	android:exported
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-103
service#io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:19
	android:exported
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-95
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-78
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-75
receiver#io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-30:20
	android:exported
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
	android:permission
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-73
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-96
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
meta-data#delivery_metrics_exported_to_big_query_enabled
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:37
	android:value
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-34
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-74
meta-data#firebase_messaging_auto_init_enabled
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-37:36
	android:value
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-33
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-64
meta-data#firebase_messaging_notification_delegation_enabled
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-40:37
	android:value
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-34
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-78
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-43:32
	android:value
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-29
	android:name
		ADDED from [:react-native-firebase_messaging] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-89
meta-data#app_data_collection_default_enabled
ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
	android:value
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
	android:name
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:19:9-28:19
MERGED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:19:9-28:19
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:11:9-20:19
MERGED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:11:9-20:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040e1442ba0e148f23a463c46367ee78\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040e1442ba0e148f23a463c46367ee78\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2659dd4f697685d621dafb416377432a\transformed\firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2659dd4f697685d621dafb416377432a\transformed\firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4620ae1f56d345a5a969c47e5df67d9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4620ae1f56d345a5a969c47e5df67d9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	tools:targetApi
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-32
	android:directBootAware
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
	android:name
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
meta-data#com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar
ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
provider#io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider
ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
	android:authorities
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
	android:exported
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
	android:initOrder
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
	android:name
		ADDED from [:react-native-firebase_app] C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-19:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-73
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:21-70
intent#action:name:android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-24:18
action#android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-80
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:21-77
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:9-40:19
	android:enabled
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-36
	android:exported
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-37
	tools:ignore
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-40
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-35:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:17-94
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:25-91
meta-data#photopicker_activity:0:required
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-39:36
	android:value
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:17-33
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-63
activity#com.canhub.cropper.CropImageActivity
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:9-44:59
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:33:9-35:39
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:33:9-35:39
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-56
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-64
provider#expo.modules.imagepicker.fileprovider.ImagePickerFileProvider
ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:9-54:20
	android:grantUriPermissions
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:50:13-47
	android:authorities
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:48:13-75
	android:exported
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:49:13-37
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:47:13-89
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
service#expo.modules.notifications.service.ExpoFirebaseMessagingService
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-17:19
	android:exported
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-91
receiver#expo.modules.notifications.service.NotificationsService
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:20
	android:enabled
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-35
	android:exported
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-83
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:android.intent.action.REBOOT+action:name:com.htc.intent.action.QUICKBOOT_POWERON+action:name:expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-30:29
	android:priority
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:28-49
action#expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:17-88
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:25-85
action#android.intent.action.BOOT_COMPLETED
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
action#android.intent.action.REBOOT
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-71
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:25-68
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
activity#expo.modules.notifications.service.NotificationForwarderActivity
ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-40:75
	android:excludeFromRecents
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-46
	android:launchMode
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-42
	android:noHistory
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-37
	android:exported
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-37
	android:theme
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-72
	android:taskAffinity
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-36
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-92
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-90
	android:name
		ADDED from [:expo-web-browser] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-87
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecb773877a9c128b02803682d93fa94e\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecb773877a9c128b02803682d93fa94e\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecb773877a9c128b02803682d93fa94e\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb5951a603c6ef86f8f4868bb3898655\transformed\react-android-0.76.9-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb5951a603c6ef86f8f4868bb3898655\transformed\react-android-0.76.9-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb5951a603c6ef86f8f4868bb3898655\transformed\react-android-0.76.9-debug\AndroidManifest.xml:20:13-77
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
meta-data#com.google.android.gms.version
ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2622c172769272601e11d1bc02c91d1e\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e754318e29fb6f6717a4ed39daeac16\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e754318e29fb6f6717a4ed39daeac16\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2622c172769272601e11d1bc02c91d1e\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
	android:name
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2622c172769272601e11d1bc02c91d1e\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\873559976d7f26baec02e4d65467cc23\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\873559976d7f26baec02e4d65467cc23\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0944679aabcfbd51c30d7f4ddb15c8b\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0944679aabcfbd51c30d7f4ddb15c8b\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c256da03d67f1bfe3b479119c06d9d5\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0944679aabcfbd51c30d7f4ddb15c8b\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0944679aabcfbd51c30d7f4ddb15c8b\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0944679aabcfbd51c30d7f4ddb15c8b\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37ac98350376969e1391bf2a167828b7\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5fbf6908f3e082e261c13278e081e6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5fbf6908f3e082e261c13278e081e6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5fbf6908f3e082e261c13278e081e6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5fbf6908f3e082e261c13278e081e6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
intent#action:name:android.intent.action.VIEW+data:scheme:https
ADDED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:11:9-15:18
meta-data#com.google.firebase.components:com.google.firebase.inappmessaging.display.FirebaseInAppMessagingDisplayKtxRegistrar
ADDED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:22:13-24:85
	android:value
		ADDED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:24:17-82
	android:name
		ADDED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:23:17-147
meta-data#com.google.firebase.components:com.google.firebase.inappmessaging.display.FirebaseInAppMessagingDisplayRegistrar
ADDED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-inappmessaging-display:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\AndroidManifest.xml:26:17-144
meta-data#com.google.firebase.components:com.google.firebase.inappmessaging.FirebaseInAppMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:14:13-16:85
	android:value
		ADDED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:16:17-82
	android:name
		ADDED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:15:17-132
meta-data#com.google.firebase.components:com.google.firebase.inappmessaging.FirebaseInAppMessagingRegistrar
ADDED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-inappmessaging:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\528f5dc0d7ec366f58bc58129094b313\transformed\firebase-inappmessaging-21.0.2\AndroidManifest.xml:18:17-129
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f206bd0f9e20beba1bb070ee3a1aae85\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040e1442ba0e148f23a463c46367ee78\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040e1442ba0e148f23a463c46367ee78\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040e1442ba0e148f23a463c46367ee78\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3aea21cae1476c6763bf0c973e65b382\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3aea21cae1476c6763bf0c973e65b382\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
intent#action:name:android.intent.action.GET_CONTENT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
action#android.intent.action.GET_CONTENT
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
category#android.intent.category.OPENABLE
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:13-73
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:23-70
provider#com.canhub.cropper.CropFileProvider
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
	android:grantUriPermissions
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
	android:authorities
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5032a21c15f221f6654cd901d0f3b5d7\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5032a21c15f221f6654cd901d0f3b5d7\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\896c9732ce3c841382c5626265e136b7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\896c9732ce3c841382c5626265e136b7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8947f1857e365b4dfc8d68df8e4505d1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8947f1857e365b4dfc8d68df8e4505d1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5032a21c15f221f6654cd901d0f3b5d7\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5032a21c15f221f6654cd901d0f3b5d7\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5032a21c15f221f6654cd901d0f3b5d7\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.camelcase.rnstarbank.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.camelcase.rnstarbank.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\896c9732ce3c841382c5626265e136b7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\896c9732ce3c841382c5626265e136b7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\896c9732ce3c841382c5626265e136b7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
meta-data#com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar
ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2659dd4f697685d621dafb416377432a\transformed\firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2659dd4f697685d621dafb416377432a\transformed\firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2659dd4f697685d621dafb416377432a\transformed\firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b49263e9abfea8d715f2aa7aa2f71a\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b49263e9abfea8d715f2aa7aa2f71a\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b49263e9abfea8d715f2aa7aa2f71a\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11b49263e9abfea8d715f2aa7aa2f71a\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4620ae1f56d345a5a969c47e5df67d9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4620ae1f56d345a5a969c47e5df67d9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4620ae1f56d345a5a969c47e5df67d9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aafff7556bb518d8b6b2af3e0ed4a66c\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad6e464112d5d30bdf11a72ad2e6e4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56e7624de93b82a8b7ebe7f674242512\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56e7624de93b82a8b7ebe7f674242512\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ede25e0a6a03ac506c16bf34d75bb3f2\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
