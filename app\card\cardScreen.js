import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  Image,
  TouchableOpacity,
  Dimensions,
  ImageBackground,
} from "react-native";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Colors, Default, Fonts } from "../../constants/styles";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import DashedLine from "react-native-dashed-line";
import MyStatusBar from "../../components/myStatusBar";
import { useNavigation } from "expo-router";

const { width } = Dimensions.get("window");

const CardScreen = () => {
  const navigation = useNavigation();
  const { t, i18n } = useTranslation();
  const isRtl = i18n.dir() == "rtl";

  function tr(key) {
    return t(`cardScreen:${key}`);
  }

  const currentDepositList = [
    {
      key: "1",
      title: "<PERSON> John",
      date: "12 march 2025",
      dollar: "Tsh 1,500,000",
      depositTo: "1239101",
      status: tr("active"),
      rate: "2% rate",
    },
    {
      key: "2",
      title: "CPA(T) Hakimu Saimon",
      date: "14 march 2025",
      dollar: "Tsh 3,200,000",
      depositTo: "12345678",
      status: tr("active"),
      rate: "2% rate",
    },
  ];

  const [flippedStates, setFlippedStates] = useState(
    currentDepositList.map(() => false)
  );

  const handleFlip = (index) => {
    setFlippedStates((prevStates) =>
      prevStates.map((state, i) => (i === index ? !state : state))
    );
  };

  return (
    <View style={{ flex: 1, backgroundColor: Colors.regularGrey }}>
      <MyStatusBar />

      <View style={{ flex: 1 }}>
        <ImageBackground
          source={require("../../assets/images/depositImage.png")}
          resizeMode="cover"
          style={{
            justifyContent: "center",
            alignItems: "center",
            width: width,
            height: 80,
            paddingHorizontal: Default.fixPadding * 2,
          }}
        >
          <Text style={{ ...Fonts.ExtraBold20white }}>{tr("cards")}</Text>
        </ImageBackground>

        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: Default.fixPadding * 8 }}
        >
          <View style={styles.summaryCard}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryTitle}>{tr("totalCards")}</Text>
              <Text style={styles.summaryValue}>10</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryTitle}>{tr("active")}</Text>
              <Text style={styles.summaryValue}>7</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryTitle}>{tr("blocked")}</Text>
              <Text style={styles.summaryValue}>3</Text>
            </View>
          </View>

          <Text
            style={{
              textAlign: isRtl ? "right" : "left",
              ...Fonts.SemiBold16black,
              marginTop: Default.fixPadding * 2,
              marginBottom: Default.fixPadding * 1.5,
              marginHorizontal: Default.fixPadding * 2,
            }}
          >
            {tr("activeCards")}
          </Text>

          {currentDepositList.map((item, index) => (
            <TouchableOpacity
              key={item.key}
              onPress={() => handleFlip(index)}
              style={{
                marginHorizontal: Default.fixPadding * 2,
                marginBottom: Default.fixPadding * 2,
                borderRadius: 10,
                backgroundColor: Colors.white,
                ...Default.shadow,
              }}
            >
              <Image
                source={
                  flippedStates[index]
                    ? require("../../assets/images/cardback1.png")
                    : require("../../assets/images/card1.png")
                }
                style={{
                  width: "100%",
                  height: 200,
                  borderRadius: 10,
                }}
                resizeMode="contain"
              />
              <View
                style={{
                  padding: Default.fixPadding * 2,
                }}
              >
                <Text
                  numberOfLines={1}
                  style={{ ...Fonts.Bold16black, overflow: "hidden" }}
                >
                  {item.title}
                </Text>
                <Text
                  numberOfLines={1}
                  style={{
                    ...Fonts.Bold14grey,
                    marginTop: Default.fixPadding * 0.3,
                    overflow: "hidden",
                  }}
                >
                  {item.date}
                </Text>
                <Text
                  numberOfLines={1}
                  style={{
                    ...Fonts.Bold18black,
                    marginTop: Default.fixPadding * 0.5,
                  }}
                >
                  {item.dollar}
                </Text>
                <Text
                  numberOfLines={1}
                  style={{
                    ...Fonts.SemiBold14grey,
                    marginTop: Default.fixPadding * 0.5,
                    overflow: "hidden",
                  }}
                >
                  {tr("depositTo")}: {item.depositTo}
                </Text>
                <Text
                  numberOfLines={1}
                  style={{
                    ...Fonts.SemiBold14grey,
                    marginTop: Default.fixPadding * 0.5,
                    overflow: "hidden",
                  }}
                >
                  {tr("status")}: {item.status}
                </Text>
                <Text
                  numberOfLines={1}
                  style={{
                    ...Fonts.SemiBold14grey,
                    marginTop: Default.fixPadding * 0.5,
                    overflow: "hidden",
                  }}
                >
                  {tr("rate")}: {item.rate}
                </Text>
              </View>
            </TouchableOpacity>
          ))}

          <Text
            style={{
              textAlign: isRtl ? "right" : "left",
              ...Fonts.SemiBold16black,
              marginBottom: Default.fixPadding * 1.5,
              marginHorizontal: Default.fixPadding * 2,
            }}
          >
            {tr("blockedCards")}
          </Text>

          <View
            style={{
              marginHorizontal: Default.fixPadding * 2,
              marginBottom: Default.fixPadding * 2,
              borderRadius: 10,
              backgroundColor: Colors.white,
              ...Default.shadow,
            }}
          >
            <View
              style={{
                flexDirection: isRtl ? "row-reverse" : "row",
                justifyContent: "space-between",
                alignItems: "center",
                paddingTop: Default.fixPadding * 1.4,
                paddingBottom: Default.fixPadding * 1.8,
                paddingHorizontal: Default.fixPadding * 1.8,
              }}
            >
              <View
                style={{
                  flex: 1,
                  flexDirection: isRtl ? "row-reverse" : "row",
                  alignItems: "center",
                }}
              >
                <View
                  style={{
                    justifyContent: "center",
                    alignItems: "center",
                    width: 38,
                    height: 38,
                    borderRadius: 19,
                    backgroundColor: Colors.lightGrey,
                  }}
                >
                  <Image
                    resizeMode="contain"
                    source={require("../../assets/images/primaryDeposit.png")}
                    style={{
                      width: 22,
                      height: 22,
                    }}
                  />
                </View>

                <View
                  style={{
                    flex: 1,
                    alignItems: isRtl ? "flex-end" : "flex-start",
                    paddingHorizontal: Default.fixPadding * 1.5,
                  }}
                >
                  <Text
                    numberOfLines={1}
                    style={{ ...Fonts.Bold16black, overflow: "hidden" }}
                  >
                    Bahari Company Limited
                  </Text>
                  <Text
                    numberOfLines={1}
                    style={{
                      ...Fonts.Bold14grey,
                      marginTop: Default.fixPadding * 0.3,
                      overflow: "hidden",
                    }}
                  >
                    10 march 2025
                  </Text>
                </View>
              </View>

              <Text
                numberOfLines={1}
                style={{ ...Fonts.Bold18black, maxWidth: 100 }}
              >
                Tsh 600k
              </Text>
            </View>

            <DashedLine
              dashGap={2}
              dashLength={2}
              dashThickness={1.5}
              dashColor={Colors.primary}
            />

            <View
              style={{
                flexDirection: isRtl ? "row-reverse" : "row",
                justifyContent: "space-between",
                alignItems: "center",
                paddingVertical: Default.fixPadding,
                paddingHorizontal: Default.fixPadding * 2,
              }}
            >
              <View
                style={{
                  flex: 1,
                  justifyContent: "center",
                  alignItems: isRtl ? "flex-end" : "flex-start",
                }}
              >
                <Text
                  numberOfLines={1}
                  style={{ ...Fonts.SemiBold14grey, overflow: "hidden" }}
                >
                  {tr("depositTo")}
                </Text>
                <Text
                  numberOfLines={1}
                  style={{
                    ...Fonts.SemiBold15black,
                    marginTop: Default.fixPadding * 0.3,
                  }}
                >
                  1234 5678 9101
                </Text>
              </View>
              <View
                style={{
                  flex: 1,
                  justifyContent: "center",
                  alignItems: "center",
                  marginHorizontal: Default.fixPadding * 0.2,
                }}
              >
                <Text
                  numberOfLines={1}
                  style={{ ...Fonts.SemiBold14grey, overflow: "hidden" }}
                >
                  {tr("status")}
                </Text>
                <Text
                  numberOfLines={1}
                  style={{
                    ...Fonts.SemiBold15green,
                    marginTop: Default.fixPadding * 0.3,
                  }}
                >
                  {tr("completed")}
                </Text>
              </View>
              <View
                style={{
                  flex: 1,
                  justifyContent: "center",
                  alignItems: isRtl ? "flex-start" : "flex-end",
                }}
              >
                <Text
                  numberOfLines={1}
                  style={{ ...Fonts.SemiBold14grey, overflow: "hidden" }}
                >
                  {tr("rate")}
                </Text>
                <Text
                  numberOfLines={1}
                  style={{
                    ...Fonts.SemiBold15black,
                    marginTop: Default.fixPadding * 0.3,
                  }}
                >
                  2% rate
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>

        <TouchableOpacity
          onPress={() => navigation.push("cardManagement/addNewCard")}
          style={{
            ...styles.addBtn,
            right: isRtl ? null : Default.fixPadding * 2,
            left: isRtl ? Default.fixPadding * 2 : null,
          }}
        >
          <MaterialIcons name="add" size={30} color={Colors.white} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default CardScreen;

const styles = StyleSheet.create({
  addBtn: {
    position: "absolute",
    justifyContent: "center",
    alignItems: "center",
    width: 60,
    height: 60,
    borderRadius: 30,
    bottom: Default.fixPadding * 2,
    backgroundColor: Colors.primary,
  },
  summaryCard: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: Default.fixPadding * 2,
    margin: Default.fixPadding * 2,
    borderRadius: 10,
    backgroundColor: Colors.white,
    ...Default.shadow,
  },
  summaryItem: {
    flex: 1,
    alignItems: "center",
    borderRightWidth: 1,
    borderRightColor: Colors.lightGrey,
    paddingHorizontal: Default.fixPadding,
  },
  summaryItemLast: {
    flex: 1,
    alignItems: "center",
    paddingHorizontal: Default.fixPadding,
  },
  summaryTitle: {
    ...Fonts.SemiBold16grey,
    marginBottom: Default.fixPadding * 0.5,
  },
  summaryValue: {
    ...Fonts.Bold18black,
  },
});