{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a43c68922964e2a138ddfb9cf7287a27\\transformed\\core-1.13.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "35,36,37,38,39,40,41,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3149,3244,3346,3444,3547,3653,3758,14129", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "3239,3341,3439,3542,3648,3753,3873,14225"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb5951a603c6ef86f8f4868bb3898655\\transformed\\react-android-0.76.9-debug\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,201,274,342,422,490,565,643,725,813,887,966,1047,1124,1207,1290,1368,1442,1513,1596,1671,1755,1825", "endColumns": "70,74,72,67,79,67,74,77,81,87,73,78,80,76,82,82,77,73,70,82,74,83,69,78", "endOffsets": "121,196,269,337,417,485,560,638,720,808,882,961,1042,1119,1202,1285,1363,1437,1508,1591,1666,1750,1820,1899"}, "to": {"startLines": "33,47,78,85,86,102,103,104,142,143,146,147,150,151,152,154,155,157,159,160,162,165,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2993,4349,7676,8134,8202,9415,9483,9558,12632,12714,12969,13043,13277,13358,13435,13593,13676,13829,13975,14046,14230,14453,14659,14729", "endColumns": "70,74,72,67,79,67,74,77,81,87,73,78,80,76,82,82,77,73,70,82,74,83,69,78", "endOffsets": "3059,4419,7744,8197,8277,9478,9553,9631,12709,12797,13038,13117,13353,13430,13513,13671,13749,13898,14041,14124,14300,14532,14724,14803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1e754318e29fb6f6717a4ed39daeac16\\transformed\\play-services-basement-18.4.0\\res\\values-et\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5422", "endColumns": "139", "endOffsets": "5557"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b7a298e4f795a18f3d41262269705afc\\transformed\\browser-1.6.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,362", "endColumns": "99,99,106,98", "endOffsets": "150,250,357,456"}, "to": {"startLines": "66,75,76,77", "startColumns": "4,4,4,4", "startOffsets": "6642,7370,7470,7577", "endColumns": "99,99,106,98", "endOffsets": "6737,7465,7572,7671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\73718788808fe748888117607a7f7695\\transformed\\appcompat-1.7.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "271,377,476,587,673,775,892,973,1050,1142,1236,1332,1434,1543,1637,1738,1832,1924,2017,2100,2211,2315,2414,2524,2626,2725,2891,13122", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "372,471,582,668,770,887,968,1045,1137,1231,1327,1429,1538,1632,1733,1827,1919,2012,2095,2206,2310,2409,2519,2621,2720,2886,2988,13200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\960e1087eeccdf840de6823a1e7fc27f\\transformed\\ui-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,278,374,469,551,629,720,811,895,977,1062,1134,1209,1284,1356,1433,1504", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,74,74,71,76,70,121", "endOffsets": "193,273,369,464,546,624,715,806,890,972,1057,1129,1204,1279,1351,1428,1499,1621"}, "to": {"startLines": "45,46,70,71,72,87,88,138,139,144,145,149,153,156,158,163,164,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4176,4269,6948,7044,7139,8282,8360,12308,12399,12802,12884,13205,13518,13754,13903,14305,14382,14537", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,74,74,71,76,70,121", "endOffsets": "4264,4344,7039,7134,7216,8355,8446,12394,12478,12879,12964,13272,13588,13824,13970,14377,14448,14654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4b5fbf6908f3e082e261c13278e081e6\\transformed\\play-services-base-18.5.0\\res\\values-et\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,450,573,677,823,948,1060,1159,1315,1419,1579,1707,1858,1999,2058,2119", "endColumns": "98,157,122,103,145,124,111,98,155,103,159,127,150,140,58,60,83", "endOffsets": "291,449,572,676,822,947,1059,1158,1314,1418,1578,1706,1857,1998,2057,2118,2202"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4424,4527,4689,4816,4924,5074,5203,5319,5562,5722,5830,5994,6126,6281,6426,6489,6554", "endColumns": "102,161,126,107,149,128,115,102,159,107,163,131,154,144,62,64,87", "endOffsets": "4522,4684,4811,4919,5069,5198,5314,5417,5717,5825,5989,6121,6276,6421,6484,6549,6637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6f597dc2fa8134c9a491d7aedff8ce\\transformed\\foundation-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,88", "endOffsets": "140,229"}, "to": {"startLines": "169,170", "startColumns": "4,4", "startOffsets": "14808,14898", "endColumns": "89,88", "endOffsets": "14893,14982"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0218db25d639d0bf937ef580de527e1b\\transformed\\material-1.6.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,221,306,405,522,604,668,753,821,885,972,1036,1095,1167,1231,1285,1404,1464,1525,1579,1652,1785,1869,1962,2070,2150,2229,2317,2384,2450,2523,2602,2688,2761,2836,2910,2982,3070,3147,3238,3330,3402,3476,3567,3621,3690,3773,3859,3921,3985,4048,4151,4255,4352,4457", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,84,98,116,81,63,84,67,63,86,63,58,71,63,53,118,59,60,53,72,132,83,92,107,79,78,87,66,65,72,78,85,72,74,73,71,87,76,90,91,71,73,90,53,68,82,85,61,63,62,102,103,96,104,80", "endOffsets": "216,301,400,517,599,663,748,816,880,967,1031,1090,1162,1226,1280,1399,1459,1520,1574,1647,1780,1864,1957,2065,2145,2224,2312,2379,2445,2518,2597,2683,2756,2831,2905,2977,3065,3142,3233,3325,3397,3471,3562,3616,3685,3768,3854,3916,3980,4043,4146,4250,4347,4452,4533"}, "to": {"startLines": "2,34,42,43,44,73,74,84,89,90,91,92,93,94,95,96,97,98,99,100,101,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3064,3878,3977,4094,7221,7285,8066,8451,8515,8602,8666,8725,8797,8861,8915,9034,9094,9155,9209,9282,9636,9720,9813,9921,10001,10080,10168,10235,10301,10374,10453,10539,10612,10687,10761,10833,10921,10998,11089,11181,11253,11327,11418,11472,11541,11624,11710,11772,11836,11899,12002,12106,12203,12483", "endLines": "5,34,42,43,44,73,74,84,89,90,91,92,93,94,95,96,97,98,99,100,101,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,140", "endColumns": "12,84,98,116,81,63,84,67,63,86,63,58,71,63,53,118,59,60,53,72,132,83,92,107,79,78,87,66,65,72,78,85,72,74,73,71,87,76,90,91,71,73,90,53,68,82,85,61,63,62,102,103,96,104,80", "endOffsets": "266,3144,3972,4089,4171,7280,7365,8129,8510,8597,8661,8720,8792,8856,8910,9029,9089,9150,9204,9277,9410,9715,9808,9916,9996,10075,10163,10230,10296,10369,10448,10534,10607,10682,10756,10828,10916,10993,11084,11176,11248,11322,11413,11467,11536,11619,11705,11767,11831,11894,11997,12101,12198,12303,12559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\479cab5183b5be1afec009352c628583\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,206,261,312,389,462,525,578", "endColumns": "103,46,54,50,76,72,62,52,67", "endOffsets": "154,201,256,307,384,457,520,573,641"}, "to": {"startLines": "67,68,69,79,80,81,82,83,141", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6742,6846,6893,7749,7800,7877,7950,8013,12564", "endColumns": "103,46,54,50,76,72,62,52,67", "endOffsets": "6841,6888,6943,7795,7872,7945,8008,8061,12627"}}]}]}