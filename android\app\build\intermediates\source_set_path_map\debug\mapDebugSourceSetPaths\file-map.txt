com.camelcase.rnstarbank.app-material-1.6.1-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0218db25d639d0bf937ef580de527e1b\transformed\material-1.6.1\res
com.camelcase.rnstarbank.app-runtime-saveable-release-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a2273837b1be25121d119f83fea1801\transformed\runtime-saveable-release\res
com.camelcase.rnstarbank.app-transition-1.2.0-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c1b6164e7cec16992ddd23c88884d02\transformed\transition-1.2.0\res
com.camelcase.rnstarbank.app-play-services-maps-18.2.0-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071f5ec04a388496e53a60c179ee94\transformed\play-services-maps-18.2.0\res
com.camelcase.rnstarbank.app-lifecycle-viewmodel-ktx-2.8.3-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15283da5e505f655373149df0b70856f\transformed\lifecycle-viewmodel-ktx-2.8.3\res
com.camelcase.rnstarbank.app-lifecycle-livedata-core-2.8.3-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\159f2a6a3f03b4c7799afbf3ac7ada7f\transformed\lifecycle-livedata-core-2.8.3\res
com.camelcase.rnstarbank.app-lifecycle-livedata-2.8.3-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15d0d2d50d5aacfdc54be73b75950dea\transformed\lifecycle-livedata-2.8.3\res
com.camelcase.rnstarbank.app-play-services-basement-18.4.0-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e754318e29fb6f6717a4ed39daeac16\transformed\play-services-basement-18.4.0\res
com.camelcase.rnstarbank.app-cardview-1.0.0-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\216ac7c3cf18ea9238c3318b1449c235\transformed\cardview-1.0.0\res
com.camelcase.rnstarbank.app-android-maps-utils-3.8.2-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2622c172769272601e11d1bc02c91d1e\transformed\android-maps-utils-3.8.2\res
com.camelcase.rnstarbank.app-camera-video-1.4.1-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a02cb9b14d89bb5fc638dece9e2f2d1\transformed\camera-video-1.4.1\res
com.camelcase.rnstarbank.app-savedstate-1.2.1-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\305726b97d29bca95f2a9919106fc8cc\transformed\savedstate-1.2.1\res
com.camelcase.rnstarbank.app-emoji2-views-helper-1.3.0-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33269302ccbb23929f7e3719747c2997\transformed\emoji2-views-helper-1.3.0\res
com.camelcase.rnstarbank.app-lifecycle-service-2.8.3-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35d0bba7488f8d3415bf1463eff56970\transformed\lifecycle-service-2.8.3\res
com.camelcase.rnstarbank.app-camera-core-1.4.1-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3aea21cae1476c6763bf0c973e65b382\transformed\camera-core-1.4.1\res
com.camelcase.rnstarbank.app-Android-Image-Cropper-4.3.1-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\479cab5183b5be1afec009352c628583\transformed\Android-Image-Cropper-4.3.1\res
com.camelcase.rnstarbank.app-ui-unit-release-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b30fea0c6acd45246c5ede9ee586743\transformed\ui-unit-release\res
com.camelcase.rnstarbank.app-play-services-base-18.5.0-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5fbf6908f3e082e261c13278e081e6\transformed\play-services-base-18.5.0\res
com.camelcase.rnstarbank.app-emoji2-1.3.0-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5032a21c15f221f6654cd901d0f3b5d7\transformed\emoji2-1.3.0\res
com.camelcase.rnstarbank.app-fragment-ktx-1.6.1-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56667a5e21fa139cd3f12e2a30457f7b\transformed\fragment-ktx-1.6.1\res
com.camelcase.rnstarbank.app-lifecycle-viewmodel-savedstate-2.8.3-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fc680ee70ddb19b37f7af7af67dcee7\transformed\lifecycle-viewmodel-savedstate-2.8.3\res
com.camelcase.rnstarbank.app-media-1.0.0-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60e52177505247937060cf7dc41401cd\transformed\media-1.0.0\res
com.camelcase.rnstarbank.app-lifecycle-runtime-compose-release-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\611f73073d401bc307977a28434f71ab\transformed\lifecycle-runtime-compose-release\res
com.camelcase.rnstarbank.app-core-runtime-2.2.0-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6605430dc729ba38acdccd1fb3bf2d44\transformed\core-runtime-2.2.0\res
com.camelcase.rnstarbank.app-savedstate-ktx-1.2.1-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\670c9c0e93052a5c886fe1f0ee177b6d\transformed\savedstate-ktx-1.2.1\res
com.camelcase.rnstarbank.app-runtime-release-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68178347d6819545f5eca6ffa8d5a495\transformed\runtime-release\res
com.camelcase.rnstarbank.app-core-ktx-1.13.1-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e1b29b1680b83d0ebe0c9acee36a76d\transformed\core-ktx-1.13.1\res
com.camelcase.rnstarbank.app-ui-util-release-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6eefbfddb25fd8f1c1fc8d4ac968b4e9\transformed\ui-util-release\res
com.camelcase.rnstarbank.app-appcompat-1.7.0-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73718788808fe748888117607a7f7695\transformed\appcompat-1.7.0\res
com.camelcase.rnstarbank.app-glide-4.16.0-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73bc231410ba9a836579d89e0d507fee\transformed\glide-4.16.0\res
com.camelcase.rnstarbank.app-play-services-auth-21.3.0-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769f502c7f8937b43a8f882eb6130fc5\transformed\play-services-auth-21.3.0\res
com.camelcase.rnstarbank.app-swiperefreshlayout-1.1.0-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79ddf078242ca0d214f5e32a72aaad96\transformed\swiperefreshlayout-1.1.0\res
com.camelcase.rnstarbank.app-camera-camera2-1.4.1-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3c1bb3ff1c2816663071fef55022c1\transformed\camera-camera2-1.4.1\res
com.camelcase.rnstarbank.app-drawee-3.2.0-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7de2b47a3c4de9c22b2f27805e3135f4\transformed\drawee-3.2.0\res
com.camelcase.rnstarbank.app-activity-ktx-1.7.2-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836d0933d3bca6f5b6b1b7812fa6def1\transformed\activity-ktx-1.7.2\res
com.camelcase.rnstarbank.app-profileinstaller-1.3.1-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83edcbd5c445a16f730eb04ad14fc2c3\transformed\profileinstaller-1.3.1\res
com.camelcase.rnstarbank.app-startup-runtime-1.1.1-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8947f1857e365b4dfc8d68df8e4505d1\transformed\startup-runtime-1.1.1\res
com.camelcase.rnstarbank.app-lifecycle-process-2.8.3-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\896c9732ce3c841382c5626265e136b7\transformed\lifecycle-process-2.8.3\res
com.camelcase.rnstarbank.app-lifecycle-viewmodel-release-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f89a6be60e33ffb95323a21753d712f\transformed\lifecycle-viewmodel-release\res
com.camelcase.rnstarbank.app-fragment-1.6.1-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90a03f7809dd6a257e48f89620aebd83\transformed\fragment-1.6.1\res
com.camelcase.rnstarbank.app-firebase-inappmessaging-display-21.0.2-40 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9255414f3a7f0d9a6be0e99ebb030fdc\transformed\firebase-inappmessaging-display-21.0.2\res
com.camelcase.rnstarbank.app-customview-poolingcontainer-1.0.0-41 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9275acb2b2b0558b98b8aa51577b07f7\transformed\customview-poolingcontainer-1.0.0\res
com.camelcase.rnstarbank.app-ui-release-42 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960e1087eeccdf840de6823a1e7fc27f\transformed\ui-release\res
com.camelcase.rnstarbank.app-ui-text-release-43 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f581e06fecb51605f36caf0eb36c71e\transformed\ui-text-release\res
com.camelcase.rnstarbank.app-activity-1.7.2-44 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a24bc841b308468633aac923f07fba3b\transformed\activity-1.7.2\res
com.camelcase.rnstarbank.app-core-1.13.1-45 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a43c68922964e2a138ddfb9cf7287a27\transformed\core-1.13.1\res
com.camelcase.rnstarbank.app-animation-release-46 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adfe9288035f76c1369e9e3a0c4e42df\transformed\animation-release\res
com.camelcase.rnstarbank.app-constraintlayout-2.1.4-47 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b35cdb6c0d436743645f73ea8e12ceee\transformed\constraintlayout-2.1.4\res
com.camelcase.rnstarbank.app-foundation-layout-release-48 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b67aa6e48a58ed4f9c9a4739e0e6f811\transformed\foundation-layout-release\res
com.camelcase.rnstarbank.app-browser-1.6.0-49 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7a298e4f795a18f3d41262269705afc\transformed\browser-1.6.0\res
com.camelcase.rnstarbank.app-react-android-0.76.9-debug-50 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb5951a603c6ef86f8f4868bb3898655\transformed\react-android-0.76.9-debug\res
com.camelcase.rnstarbank.app-firebase-common-21.0.0-51 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0c3d29a1217f2a9a56e5949b90f28e\transformed\firebase-common-21.0.0\res
com.camelcase.rnstarbank.app-ui-graphics-release-52 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0707e6d21cdea09d9d3ba065f9a1443\transformed\ui-graphics-release\res
com.camelcase.rnstarbank.app-ui-geometry-release-53 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0e7fd7d2fbd60b92a42d1cdd9202d03\transformed\ui-geometry-release\res
com.camelcase.rnstarbank.app-tracing-ktx-1.2.0-54 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3d2155b6a339a432691f2cc07d932de\transformed\tracing-ktx-1.2.0\res
com.camelcase.rnstarbank.app-graphics-path-1.0.1-55 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8d9bc4bc5e1b6c56bbc4f3d45d2a55a\transformed\graphics-path-1.0.1\res
com.camelcase.rnstarbank.app-tracing-1.2.0-56 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cfaf9e3bcc37bb8684c7eca3bb881111\transformed\tracing-1.2.0\res
com.camelcase.rnstarbank.app-camera-view-1.4.1-57 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d086cf9355146b7efe734736376b37a0\transformed\camera-view-1.4.1\res
com.camelcase.rnstarbank.app-coordinatorlayout-1.2.0-58 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3042085f0d0a8c8860c1808071a0be5\transformed\coordinatorlayout-1.2.0\res
com.camelcase.rnstarbank.app-lifecycle-livedata-core-ktx-2.8.3-59 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d420bf24dfd22ae778b9c11b1ac34856\transformed\lifecycle-livedata-core-ktx-2.8.3\res
com.camelcase.rnstarbank.app-camera-lifecycle-1.4.1-60 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d676e438f7e88ea8ee000c319cdd8ce7\transformed\camera-lifecycle-1.4.1\res
com.camelcase.rnstarbank.app-viewpager2-1.1.0-61 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8054a8abed560bf50051308d630cfcc\transformed\viewpager2-1.1.0\res
com.camelcase.rnstarbank.app-drawerlayout-1.1.1-62 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d85290e8303861192802a483e38fda60\transformed\drawerlayout-1.1.1\res
com.camelcase.rnstarbank.app-firebase-messaging-24.1.2-63 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f6e1764ebbc69e7b814e0f7e5c74e2\transformed\firebase-messaging-24.1.2\res
com.camelcase.rnstarbank.app-appcompat-resources-1.7.0-64 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da83cae8ab2750a88e69c2caa9f40d56\transformed\appcompat-resources-1.7.0\res
com.camelcase.rnstarbank.app-recyclerview-1.3.1-65 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dddd298b4d9cf70ba371b0dd2aa54613\transformed\recyclerview-1.3.1\res
com.camelcase.rnstarbank.app-autofill-1.1.0-66 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e27232b23c335b4f1247529100ab75a0\transformed\autofill-1.1.0\res
com.camelcase.rnstarbank.app-camera-extensions-1.4.1-67 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3952314d8750b03c688d85478b03a47\transformed\camera-extensions-1.4.1\res
com.camelcase.rnstarbank.app-core-splashscreen-1.2.0-alpha02-68 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e416f8cc7268e73ac6b4b306987a97a2\transformed\core-splashscreen-1.2.0-alpha02\res
com.camelcase.rnstarbank.app-animation-core-release-69 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e56984a8a2f0c589681cba9092dfdaeb\transformed\animation-core-release\res
com.camelcase.rnstarbank.app-camera-mlkit-vision-1.4.1-70 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e56dfd0156b28fde4bc8f7aaf54aece4\transformed\camera-mlkit-vision-1.4.1\res
com.camelcase.rnstarbank.app-annotation-experimental-1.4.1-71 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e987ebe554e47d795a2f86bcc032caba\transformed\annotation-experimental-1.4.1\res
com.camelcase.rnstarbank.app-foundation-release-72 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee6f597dc2fa8134c9a491d7aedff8ce\transformed\foundation-release\res
com.camelcase.rnstarbank.app-work-runtime-2.7.1-73 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\efe6204fe7e60e3c6c28b8e4fad5e1f7\transformed\work-runtime-2.7.1\res
com.camelcase.rnstarbank.app-lifecycle-runtime-ktx-release-74 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5e83dca3c088a54cede7a3a7460cee7\transformed\lifecycle-runtime-ktx-release\res
com.camelcase.rnstarbank.app-lifecycle-runtime-release-75 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb9916e674b6606ead48b7e7b838ec5f\transformed\lifecycle-runtime-release\res
com.camelcase.rnstarbank.app-BlurView-version-2.0.6-76 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe21ee1e05caa973fe8a6cb87a2c4431\transformed\BlurView-version-2.0.6\res
com.camelcase.rnstarbank.app-pngs-77 C:\Users\<USER>\Desktop\safaribank\android\app\build\generated\res\pngs\debug
com.camelcase.rnstarbank.app-res-78 C:\Users\<USER>\Desktop\safaribank\android\app\build\generated\res\processDebugGoogleServices
com.camelcase.rnstarbank.app-resValues-79 C:\Users\<USER>\Desktop\safaribank\android\app\build\generated\res\resValues\debug
com.camelcase.rnstarbank.app-packageDebugResources-80 C:\Users\<USER>\Desktop\safaribank\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.camelcase.rnstarbank.app-packageDebugResources-81 C:\Users\<USER>\Desktop\safaribank\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.camelcase.rnstarbank.app-debug-82 C:\Users\<USER>\Desktop\safaribank\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.camelcase.rnstarbank.app-debug-83 C:\Users\<USER>\Desktop\safaribank\android\app\src\debug\res
com.camelcase.rnstarbank.app-main-84 C:\Users\<USER>\Desktop\safaribank\android\app\src\main\res
com.camelcase.rnstarbank.app-debug-85 C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-86 C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\app\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-87 C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\in-app-messaging\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-88 C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-firebase\messaging\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-89 C:\Users\<USER>\Desktop\safaribank\node_modules\@react-native-picker\picker\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-90 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-application\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-91 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-asset\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-92 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-blur\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-93 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-camera\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-94 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-constants\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-95 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-file-system\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-96 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-font\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-97 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-haptics\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-98 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-loader\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-99 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-image-picker\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-100 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-keep-awake\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-101 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-linking\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-102 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-modules-core\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-103 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-notifications\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-104 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-splash-screen\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-105 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-system-ui\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-106 C:\Users\<USER>\Desktop\safaribank\node_modules\expo-web-browser\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-107 C:\Users\<USER>\Desktop\safaribank\node_modules\expo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-108 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-109 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-maps\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-110 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-pager-view\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-111 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-112 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-113 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-114 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-svg\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-115 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-vector-icons\android\build\intermediates\packaged_res\debug\packageDebugResources
com.camelcase.rnstarbank.app-debug-116 C:\Users\<USER>\Desktop\safaribank\node_modules\react-native-webview\android\build\intermediates\packaged_res\debug\packageDebugResources
