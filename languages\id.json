{"onboardingScreen": {"title1": "Operasi Perbankan yang <PERSON>an dan <PERSON>", "title2": "<PERSON><PERSON><PERSON> u<PERSON> dengan bijak", "title3": "Temukan bank dan atm di sekitar Anda", "description": "Lorem ipsum dolor duduk amet, consectetur.Penyakit Elemen <PERSON>.Siapa pun yang berinvestasi di elemen pisang adalah masalah paket.", "skip": "<PERSON><PERSON><PERSON>", "tapBack": "Ketuk Kembali Lagi untuk Keluar dari Aplikasi"}, "loginScreen": {"login": "<PERSON><PERSON><PERSON>", "welcomeBack": "Selamat Datang kembali", "happy": "<PERSON><PERSON> senang bertemu denganmu lagi", "mobileNumber": "<PERSON><PERSON><PERSON><PERSON> nomor ponsel Anda", "search": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "tapBack": "Ketuk Kembali Lagi untuk Keluar dari Aplikasi"}, "registerScreen": {"register": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON> na<PERSON>", "number": "<PERSON><PERSON><PERSON><PERSON> nomor ponsel Anda", "email": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> email <PERSON>a"}, "otpScreen": {"verification": "Verifikasi OTP", "pleaseEnter": "OTP telah dikirimkan kepada Anda di nomor ponsel <PERSON>a, silakan masukkan di bawah ini", "verify": "Memeriksa", "resend": "Ulang"}, "pinScreen": {"enterPin": "Masukkan PIN", "welcome": "Selamat datang", "enterDigit": "<PERSON> pin 4 digit", "continue": "Melanjutkan"}, "loader": {"pleaseWait": "<PERSON><PERSON> tunggu"}, "bottomTab": {"home": "<PERSON><PERSON><PERSON>", "deposit": "Menyetorkan", "loans": "<PERSON><PERSON><PERSON>", "account": "<PERSON><PERSON><PERSON>", "tapBack": "Ketuk Kembali Lagi untuk Keluar dari Aplikasi"}, "homeScreen": {"totalBalance": "Total Saldo", "savingAccount": "Tabungan", "services": "Jasa", "account": "<PERSON><PERSON><PERSON>", "fundTransfer": "Pengiri<PERSON> dana", "statement": "<PERSON><PERSON><PERSON><PERSON>", "billPay": "<PERSON><PERSON>", "scan": "Pindai dan bayar", "more": "<PERSON><PERSON>", "transaction": "Transaksi terbaru", "seeAll": "<PERSON><PERSON>a"}, "servicesScreen": {"services": "Jasa", "account": "<PERSON><PERSON><PERSON>", "fundTransfer": "Pengiri<PERSON> dana", "statement": "<PERSON><PERSON><PERSON><PERSON>", "billPay": "<PERSON><PERSON>", "scan": "Pindai dan bayar", "deposit": "Menyetorkan", "loans": "<PERSON><PERSON><PERSON>", "cards": "Kartu-kartu", "mutualFund": "<PERSON><PERSON><PERSON> dana", "insurance": "Pertanggungan", "shopOffer": "Berbelanja & Menawarkan", "recharge": "<PERSON><PERSON>"}, "latestTransactionScreen": {"transaction": "Transaksi"}, "notificationScreen": {"notification": "Pemberitahuan", "remove": "Pemberitahuan dihapus.", "noNotification": "Tidak ada pemberitahuan baru"}, "fundTransferScreen": {"fundTransfer": "Pengiri<PERSON> dana", "beneficiaryPay": "Pembayaran penerima", "payment": "Pembayaran Iban", "pay": "Imps Pay", "transferNow": "Transfer sekarang"}, "beneficiaryPayScreen": {"fromAccount": "<PERSON><PERSON> akun", "beneficiaryInfo": "Info penerima", "name": "<PERSON><PERSON>", "bankName": "Nama Bank Penerima", "accountNumber": "<PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "transferLimit": "Batas transfer", "savingAccount": "Tabungan", "currentAccount": "Akun saat ini", "salaryAccount": "<PERSON><PERSON><PERSON> gaji", "nriAccount": "Akun N<PERSON>"}, "paymentIBANScreen": {"fromAccount": "<PERSON><PERSON> akun", "beneficiaryInfo": "Info penerima", "number": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "code": "Kode BIC", "bank": "Bank Penerima", "amount": "<PERSON><PERSON><PERSON>", "remark": "Komentar", "savingAccount": "Tabungan", "currentAccount": "Akun saat ini", "salaryAccount": "<PERSON><PERSON><PERSON> gaji", "nriAccount": "Akun N<PERSON>"}, "payIMPSScreen": {"fromAccount": "<PERSON><PERSON> akun", "accountNo": "Masukkan akun no", "holderName": "<PERSON><PERSON> pemilik akun", "enterHolderName": "<PERSON><PERSON><PERSON>n nama pemegang akun", "toAccount": "<PERSON> rekening", "enterAccountNo": "Masukkan akun no", "bankName": "nama Bank", "hBank": "HDFC Bank", "iCode": "Kode ifsc", "enterICode": "Masukkan kode IFSC", "amount": "<PERSON><PERSON><PERSON>", "enterAmount": "<PERSON><PERSON><PERSON> j<PERSON>", "savingAccount": "Tabungan", "currentAccount": "Akun saat ini", "salaryAccount": "<PERSON><PERSON><PERSON> gaji", "nriAccount": "Akun N<PERSON>"}, "successfullyScreen": {"successfully": "Transfer berhasil", "transferred": "Ditransfer ke", "from": "<PERSON><PERSON>", "remark": "Komentar", "amount": "<PERSON><PERSON><PERSON>", "paymentMode": "Mode pembayaran", "backToHome": "<PERSON><PERSON><PERSON> ke rumah"}, "accountDetailScreen": {"account": "<PERSON><PERSON><PERSON>", "totalBalance": "Total Saldo", "accountNumber": "<PERSON><PERSON> akun", "accountDetail": "Detail akun", "cif": "cif", "ifsc": "Ifsc", "branchCode": "<PERSON><PERSON> cabang", "branchName": "<PERSON><PERSON> cabang", "openingDate": "<PERSON><PERSON>", "mmid": "<PERSON><PERSON><PERSON>", "currentAccount": "Akun saat ini", "saving": "Tabungan", "salaryAccount": "<PERSON><PERSON><PERSON> gaji", "nRIAccount": "Akun N<PERSON>", "viewStateMent": "<PERSON><PERSON><PERSON><PERSON>"}, "statementScreen": {"statement": "<PERSON><PERSON><PERSON><PERSON>", "totalBalance": "Total Saldo", "selectDate": "<PERSON><PERSON><PERSON>", "go": "<PERSON><PERSON>", "transaction": "<PERSON><PERSON>", "currentAccount": "Akun saat ini", "savingAccount": "Tabungan", "salaryAccount": "<PERSON><PERSON><PERSON> gaji", "nriAccount": "Akun N<PERSON>", "cancel": "Membatalkan", "ok": "<PERSON>e"}, "depositScreen": {"deposit": "Menyetorkan", "currentDeposit": "Setoran saat ini", "depositTo": "<PERSON><PERSON> ke:", "status": "status:", "rate": "Kecepatan:", "pending": "Tertunda", "completedDeposit": "<PERSON><PERSON>", "completed": "Lengka<PERSON>"}, "addDepositScreen": {"addDeposit": "Tambahkan setoran", "depositPeriod": "<PERSON><PERSON><PERSON> periode setoran", "amount": "<PERSON><PERSON><PERSON>", "enterDeposit": "<PERSON><PERSON><PERSON><PERSON>", "yourRate": "<PERSON><PERSON><PERSON> 8%", "depositTo": "<PERSON><PERSON> ke", "selectAmount": "<PERSON><PERSON><PERSON>", "uploadImage": "Unggah gambar cek", "frontSideImage": "<PERSON>ggah periksa gambar sisi depan", "backSideImage": "<PERSON>ggah periksa gambar sisi belakang", "depositNow": "<PERSON><PERSON>", "savingAccount": "Tabungan", "currentAccount": "Akun saat ini", "salaryAccount": "<PERSON><PERSON><PERSON> gaji", "nriAccount": "Akun N<PERSON>"}, "successDepositModal": {"success": "<PERSON><PERSON><PERSON><PERSON>", "congratulation": "Selamat $ 1000 Anda berhasil disetor selama 18 bulan", "okay": "<PERSON>e"}, "loansScreen": {"loans": "<PERSON><PERSON><PERSON>", "makeEducation": "Jadikan Pendidikan Prioritas Anda", "lowestInterest": "Suku bunga terendah", "theRightChoiceCar": "<PERSON><PERSON><PERSON> yang tepat untuk membiayai mobil Anda", "dreamCar": "Beli Mobil Imp<PERSON>", "applyNow": "<PERSON><PERSON><PERSON> se<PERSON>", "currentLoans": "<PERSON><PERSON>man saat ini", "period": "Periode", "rate": "Kecepatan", "eMI": "emi", "viewStatement": "<PERSON><PERSON><PERSON><PERSON>", "homeLoan": "<PERSON><PERSON><PERSON> rumah", "carLoan": "Pinjaman mobil"}, "loansStatementScreen": {"statement": "<PERSON><PERSON><PERSON><PERSON>", "period": "Periode", "rate": "Kecepatan", "eMI": "emi", "recentTransaction": "Transaksi terbaru"}, "educationLoanScreen": {"educationLoan": "<PERSON><PERSON><PERSON>", "makeEducation": "Jadikan Pendidikan Prioritas Anda", "description": "Lorem ipsum dolor duduk amet, consectetur.Doxposueretellus Za Weekend Weekend Lapisan Produksi Terbaru Ipsuodignissim Nulla Element Clinical A Pesan.Magna Invest Xmorbiuf Quiver Cartoon.Sekarang kelaparan cokelat dan faucibuxattis daerah sedih.Tapi Ultricies Mass Pool Sagittntegelorem.Tetapi setiap orang selalu menjadi survei proidiasollictudinvulputate.", "phoneNumber": "Nomor telepon", "enterNumber": "Ma<PERSON>kkan nomor telepon Anda", "message": "<PERSON><PERSON>", "writeMessage": "<PERSON><PERSON> p<PERSON>", "interested": "saya te<PERSON><PERSON>"}, "educationLoanModal": {"educationLoan": "<PERSON><PERSON><PERSON>", "thankYou": "<PERSON><PERSON> kasih atas pinjaman pendidikan yang tertarik, kami akan segera mengh<PERSON>i", "okay": "<PERSON>e"}, "accountScreen": {"account": "<PERSON><PERSON><PERSON>", "nearbyBank": "Bank terdekat", "nearbyATMs": "ATM terdekat", "changePin": "Ganti PIN", "language": "Bahasa", "PrivacyPolicy": "Kebijakan pribadi", "termsCondition": "Syarat & Kondisi", "customerSupport": "Dukungan pelanggan", "logout": "<PERSON><PERSON><PERSON>"}, "editProfileScreen": {"editProfile": "Sunting profil", "name": "<PERSON><PERSON>", "enterName": "<PERSON><PERSON><PERSON><PERSON> na<PERSON>", "email": "<PERSON><PERSON><PERSON> email", "enterEmail": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> email <PERSON>a", "mobile": "Nomor telepon", "enterMobile": "Ma<PERSON>kkan nomor telepon Anda", "update": "<PERSON><PERSON><PERSON><PERSON>", "changeProfile": "Unggah gambar", "camera": "<PERSON><PERSON><PERSON>", "gallery": "<PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON>", "removeImage": "Foto profil dihapus.", "ok": "<PERSON>e", "deny": "Anda telah membantah untuk mengakses kamera !!!"}, "nearByScreen": {"direction": "<PERSON><PERSON>", "youAreHere": "<PERSON><PERSON> di sini", "nearbyBank": "Bank terdekat"}, "changePinScreen": {"changePin": "Ganti PIN", "currentPin": "PIN sekarang", "enterCurrentPin": "<PERSON><PERSON><PERSON><PERSON> pin saat ini", "newPin": "Pin baru", "enterNewPin": "<PERSON><PERSON><PERSON><PERSON> pin baru", "confirmPin": "Konfirmasi pin", "confirmNewPin": "Kon<PERSON>rmasik<PERSON> pin baru <PERSON>a", "reset": "<PERSON><PERSON><PERSON>"}, "languageScreen": {"language": "Bahasa", "update": "<PERSON><PERSON><PERSON><PERSON>"}, "termsConditionScreen": {"termsAndCondition": "<PERSON><PERSON><PERSON>", "mainDescription": "Lorem ipsum dolor duduk amet, consectetur.Doxposueretellus Za Weekend Weekend Lapisan Produksi Terbaru Ipsuodignissim Nulla Element Clinical A Pesan.Magna Invest Xmorbiuf Quiver Cartoon.Sekarang kelaparan cokelat dan faucibuxattis daerah sedih.Tapi Ultricies Mass Pool Sagittntegelorem.Tetapi setiap orang selalu merupakan survei proidiasollicitudinvulputate.lorem carrots consectetur.dolposueretellus akhir pekan terbaru dengan asumsi elemen apa pun orci sebuah diktumst.magna placerat vmorbiuf quiver viverra. Sepak bola sekarang danau.", "subDescription": "Lorem ipsum dolor duduk amet, consectetur.Doxposueretellus Za Weekend Weekend Lapisan Produksi Terbaru Ipsuodignissim Nulla Element Clinical A Pesan.Magna Invest Xmorbiuf Quiver Cartoon.Sekarang kelaparan cokelat dan faucibuxattis daerah sedih.Tapi Ultricies Mass Pool Sagittntegelorem.Tetapi setiap orang selalu menjadi survei proidiasollictudinvulputate."}, "privacyPolicyScreen": {"privacyPolicy": "Kebijakan pribadi", "mainDescription": "Lorem ipsum dolor duduk amet, consectetur.Doxposueretellus Za Weekend Weekend Lapisan Produksi Terbaru Ipsuodignissim Nulla Element Clinical A Pesan.Magna Invest Xmorbiuf Quiver Cartoon.Sekarang kelaparan cokelat dan faucibuxattis daerah sedih.Tapi Ultricies Mass Pool Sagittntegelorem.Tetapi setiap orang selalu merupakan survei proidiasollicitudinvulputate.lorem carrots consectetur.dolposueretellus akhir pekan terbaru dengan asumsi elemen apa pun orci sebuah diktumst.magna placerat vmorbiuf quiver viverra. Sepak bola sekarang danau.", "subDescription": "Lorem ipsum dolor duduk amet, consectetur.Doxposueretellus Za Weekend Weekend Lapisan Produksi Terbaru Ipsuodignissim Nulla Element Clinical A Pesan.Magna Invest Xmorbiuf Quiver Cartoon.Sekarang kelaparan cokelat dan faucibuxattis daerah sedih.Tapi Ultricies Mass Pool Sagittntegelorem.Tetapi setiap orang selalu menjadi survei proidiasollictudinvulputate."}, "customerSupportScreen": {"customerSupport": "Dukungan pelanggan", "weAreHere": "Kami di sini untuk membantu jadi silakan menghubungi kami.", "name": "<PERSON><PERSON>", "enterName": "<PERSON><PERSON><PERSON><PERSON> na<PERSON>", "email": "<PERSON><PERSON><PERSON> email", "enterEmail": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> email <PERSON>a", "message": "<PERSON><PERSON>", "enterMessage": "<PERSON><PERSON><PERSON><PERSON> pesan", "submit": "<PERSON><PERSON>"}, "logoutModal": {"logout": "<PERSON><PERSON><PERSON>", "areYouSure": "<PERSON><PERSON><PERSON><PERSON> Anda ingin keluar?", "cancel": "Membatalkan", "yes": "Ya"}}