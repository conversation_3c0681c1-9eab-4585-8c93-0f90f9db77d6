import React, { useState } from 'react';
import { View, TextInput, TouchableOpacity, Alert } from 'react-native';
import { Colors, Default, Fonts } from '../constants/styles';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';

const MessageInput = ({ onSendMessage, placeholder, isRtl, disabled = false }) => {
  const { t } = useTranslation();
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);

  const handleSend = async () => {
    if (!message.trim() || isSending || disabled) return;

    try {
      setIsSending(true);
      await onSendMessage(message.trim());
      setMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message. Please try again.');
    } finally {
      setIsSending(false);
    }
  };

  return (
    <View
      style={{
        flexDirection: isRtl ? 'row-reverse' : 'row',
        alignItems: 'flex-end',
        paddingHorizontal: Default.fixPadding * 2,
        paddingVertical: Default.fixPadding,
        backgroundColor: Colors.white,
        borderTopWidth: 1,
        borderTopColor: Colors.lightGrey,
      }}
    >
      <View
        style={{
          flex: 1,
          backgroundColor: Colors.extraLightGrey,
          borderRadius: 25,
          paddingHorizontal: Default.fixPadding * 1.5,
          paddingVertical: Default.fixPadding,
          marginRight: isRtl ? 0 : Default.fixPadding,
          marginLeft: isRtl ? Default.fixPadding : 0,
          maxHeight: 100,
        }}
      >
        <TextInput
          value={message}
          onChangeText={setMessage}
          placeholder={placeholder || 'Type a message...'}
          placeholderTextColor={Colors.grey}
          multiline
          textAlignVertical="top"
          style={{
            ...Fonts.Regular14black,
            textAlign: isRtl ? 'right' : 'left',
            minHeight: 20,
            maxHeight: 80,
            padding: 0,
          }}
          editable={!disabled && !isSending}
        />
      </View>

      <TouchableOpacity
        onPress={handleSend}
        disabled={!message.trim() || isSending || disabled}
        style={{
          width: 44,
          height: 44,
          borderRadius: 22,
          backgroundColor: (!message.trim() || isSending || disabled) ? Colors.grey : Colors.primary,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <MaterialCommunityIcons
          name={isSending ? 'loading' : 'send'}
          size={20}
          color={Colors.white}
          style={{
            transform: [{ rotate: isSending ? '0deg' : (isRtl ? '180deg' : '0deg') }],
          }}
        />
      </TouchableOpacity>
    </View>
  );
};

export default MessageInput;