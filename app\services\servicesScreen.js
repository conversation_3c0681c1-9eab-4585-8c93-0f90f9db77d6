import {
  Text,
  View,
  FlatList,
  TouchableOpacity,
  Image,
  Dimensions,
} from "react-native";
import React from "react";
import { useTranslation } from "react-i18next";
import { Colors, Default, Fonts } from "../../constants/styles";
import Ionicons from "react-native-vector-icons/Ionicons";
import MyStatusBar from "../../components/myStatusBar";
import { useNavigation } from "expo-router";

const { width } = Dimensions.get("window");

const ServicesScreen = () => {
  const navigation = useNavigation();

  const { t, i18n } = useTranslation();

  const isRtl = i18n.dir() == "rtl";

  function tr(key) {
    return t(`servicesScreen:${key}`);
  }

  const servicesList = [
    /*{
      key: "1",
      image: require("../../assets/images/Services1.png"),
      title: tr("account"),
    },
    {
      key: "2",
      image: require("../../assets/images/Services2.png"),
      title: tr("fundTransfer"),
    },
    {
      key: "3",
      image: require("../../assets/images/Services3.png"),
      title: tr("statement"),
    },
    {
      key: "4",
      image: require("../../assets/images/primaryDeposit.png"),
      title: tr("invoices"),
    },
    {
      key: "8",
      image: require("../../assets/images/Services4.png"),
      title: tr("withdraw"),
    },*/
    {
      key: "5",
      image: require("../../assets/images/primaryLoans.png"),
      title: tr("loans"),
    },
    {
      key: "6",
      image: require("../../assets/images/Services7.png"),
      title: tr("cards"),
    },
    {
      key: "7",
      image: require("../../assets/images/Services5.png"),
      title: tr("pay"),
    },
    
    // {
    //   key: "9",
    //   image: require("../../assets/images/Services8.png"),
    //   title: tr("mutualFund"),
    // },
    // {
    //   key: "10",
    //   image: require("../../assets/images/Services11.png"),
    //   title: tr("insurance"),
    // },
    // {
    //   key: "11",
    //   image: require("../../assets/images/Services9.png"),
    //   title: tr("shopOffer"),
    // },
    // {
    //   key: "12",
    //   image: require("../../assets/images/Services10.png"),
    //   title: tr("recharge"),
    // },
  ];

  const servicesClickHandler = (index) => {
    if (index == 0) {
      return navigation.push("accountDetail/accountDetailScreen");
    } else if (index == 1) {
      return navigation.push("fundTransfer/fundTransferScreen");
    } else if (index == 2) {
      return navigation.push("statement/statementScreen");
    } else if (index == 3) {
      return navigation.navigate("(tabs)", { screen: "deposit/depositScreen" });
    } else if (index == 4) {
      return navigation.navigate("(tabs)", { screen: "loans/loansScreen" });
    } else if (index == 5) {
      return navigation.navigate("(tabs)", { screen: "card/cardScreen" });
    }
  };

  const renderItemServices = ({ item, index }) => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        disabled={index > 4 ? true : false}
        onPress={() => servicesClickHandler(index)}
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          paddingVertical: Default.fixPadding * 2,
          paddingHorizontal: Default.fixPadding * 0.5,
          marginHorizontal: Default.fixPadding,
          marginBottom: Default.fixPadding * 2,
          maxWidth: width / 3 - 25,
          borderRadius: 10,
          backgroundColor: Colors.white,
          ...Default.shadow,
        }}
      >
        <Image
          resizeMode="contain"
          source={item.image}
          style={{ width: 30, height: 30 }}
        />
        <Text
          numberOfLines={1}
          style={{
            ...Fonts.Bold15primary,
            overflow: "hidden",
            marginTop: Default.fixPadding * 0.5,
          }}
        >
          {item.title}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={{ flex: 1, backgroundColor: Colors.regularGrey }}>
      <MyStatusBar />
      <View
        style={{
          flexDirection: isRtl ? "row-reverse" : "row",
          alignItems: "center",
          paddingVertical: Default.fixPadding * 1.2,
          paddingHorizontal: Default.fixPadding * 2,
          backgroundColor: Colors.regularGrey,
          ...Default.shadow,
        }}
      >
        <TouchableOpacity onPress={() => navigation.pop()}>
          <Ionicons
            name={isRtl ? "arrow-forward" : "arrow-back"}
            size={23}
            color={Colors.black}
          />
        </TouchableOpacity>
        <Text
          style={{
            ...Fonts.Bold20black,
            marginHorizontal: Default.fixPadding * 1.5,
          }}
        >
          {tr("services")}
        </Text>
      </View>

      <FlatList
        numColumns={3}
        data={servicesList}
        renderItem={renderItemServices}
        keyExtractor={(item) => item.key}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingTop: Default.fixPadding * 2,
          paddingHorizontal: Default.fixPadding,
        }}
      />
    </View>
  );
};

export default ServicesScreen;
