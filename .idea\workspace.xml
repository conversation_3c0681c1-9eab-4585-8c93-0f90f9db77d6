<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="70d4c22b-225c-4a67-9f9e-5f2bcc5794cd" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/app.json" beforeDir="false" afterPath="$PROJECT_DIR$/app.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/(tabs)/_layout.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/(tabs)/explore.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/(tabs)/index.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/+not-found.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/_layout.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/assets/fonts/SpaceMono-Regular.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/assets/images/adaptive-icon.png" beforeDir="false" afterPath="$PROJECT_DIR$/assets/images/adaptive-icon.png" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/assets/images/favicon.png" beforeDir="false" afterPath="$PROJECT_DIR$/assets/images/favicon.png" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/assets/images/icon.png" beforeDir="false" afterPath="$PROJECT_DIR$/assets/images/icon.png" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/assets/images/partial-react-logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/assets/images/react-logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/assets/images/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/assets/images/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/Collapsible.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/ExternalLink.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/HapticTab.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/HelloWave.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/ParallaxScrollView.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/ThemedText.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/ThemedView.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/__tests__/ThemedText-test.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/__tests__/__snapshots__/ThemedText-test.tsx.snap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/ui/IconSymbol.ios.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/ui/IconSymbol.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/ui/TabBarBackground.ios.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/ui/TabBarBackground.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/constants/Colors.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/hooks/useColorScheme.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/hooks/useColorScheme.web.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/hooks/useThemeColor.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/reset-project.js" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "customColor": "",
  "associatedIndex": 0
}]]></component>
  <component name="ProjectId" id="2upxschvGbl6uyOyFybX6Drz3au" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "dart.analysis.tool.window.visible": "false",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/mobileapps/parentexperience",
    "show.migrate.to.gradle.popup": "false"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="70d4c22b-225c-4a67-9f9e-5f2bcc5794cd" name="Changes" comment="" />
      <created>1742959531995</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1742959531995</updated>
    </task>
    <servers />
  </component>
</project>