{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\960e1087eeccdf840de6823a1e7fc27f\\transformed\\ui-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,384,487,576,655,751,843,930,1017,1107,1184,1260,1340,1416,1494,1564", "endColumns": "98,81,97,102,88,78,95,91,86,86,89,76,75,79,75,77,69,122", "endOffsets": "199,281,379,482,571,650,746,838,925,1012,1102,1179,1255,1335,1411,1489,1559,1682"}, "to": {"startLines": "44,45,65,66,67,74,75,122,123,125,126,128,129,130,131,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4128,4227,6747,6845,6948,7590,7669,11481,11573,11743,11830,12003,12080,12156,12236,12413,12491,12561", "endColumns": "98,81,97,102,88,78,95,91,86,86,89,76,75,79,75,77,69,122", "endOffsets": "4222,4304,6840,6943,7032,7664,7760,11568,11655,11825,11915,12075,12151,12231,12307,12486,12556,12679"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0218db25d639d0bf937ef580de527e1b\\transformed\\material-1.6.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,233,319,421,549,630,695,790,860,923,1016,1088,1151,1225,1289,1345,1463,1521,1583,1639,1719,1853,1942,2023,2134,2215,2295,2385,2452,2518,2594,2676,2764,2837,2914,2984,3061,3150,3224,3318,3420,3492,3573,3677,3730,3797,3890,3979,4041,4105,4168,4279,4376,4478,4576", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,101,127,80,64,94,69,62,92,71,62,73,63,55,117,57,61,55,79,133,88,80,110,80,79,89,66,65,75,81,87,72,76,69,76,88,73,93,101,71,80,103,52,66,92,88,61,63,62,110,96,101,97,82", "endOffsets": "228,314,416,544,625,690,785,855,918,1011,1083,1146,1220,1284,1340,1458,1516,1578,1634,1714,1848,1937,2018,2129,2210,2290,2380,2447,2513,2589,2671,2759,2832,2909,2979,3056,3145,3219,3313,3415,3487,3568,3672,3725,3792,3885,3974,4036,4100,4163,4274,4371,4473,4571,4654"}, "to": {"startLines": "2,33,41,42,43,68,69,73,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2999,3817,3919,4047,7037,7102,7520,7765,7828,7921,7993,8056,8130,8194,8250,8368,8426,8488,8544,8624,8758,8847,8928,9039,9120,9200,9290,9357,9423,9499,9581,9669,9742,9819,9889,9966,10055,10129,10223,10325,10397,10478,10582,10635,10702,10795,10884,10946,11010,11073,11184,11281,11383,11660", "endLines": "5,33,41,42,43,68,69,73,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,124", "endColumns": "12,85,101,127,80,64,94,69,62,92,71,62,73,63,55,117,57,61,55,79,133,88,80,110,80,79,89,66,65,75,81,87,72,76,69,76,88,73,93,101,71,80,103,52,66,92,88,61,63,62,110,96,101,97,82", "endOffsets": "278,3080,3914,4042,4123,7097,7192,7585,7823,7916,7988,8051,8125,8189,8245,8363,8421,8483,8539,8619,8753,8842,8923,9034,9115,9195,9285,9352,9418,9494,9576,9664,9737,9814,9884,9961,10050,10124,10218,10320,10392,10473,10577,10630,10697,10790,10879,10941,11005,11068,11179,11276,11378,11476,11738"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\73718788808fe748888117607a7f7695\\transformed\\appcompat-1.7.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "283,403,512,620,705,807,923,1008,1088,1179,1272,1367,1461,1560,1653,1752,1848,1939,2030,2112,2219,2318,2417,2525,2633,2740,2899,11920", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "398,507,615,700,802,918,1003,1083,1174,1267,1362,1456,1555,1648,1747,1843,1934,2025,2107,2214,2313,2412,2520,2628,2735,2894,2994,11998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6f597dc2fa8134c9a491d7aedff8ce\\transformed\\foundation-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "136,137", "startColumns": "4,4", "startOffsets": "12684,12784", "endColumns": "99,101", "endOffsets": "12779,12881"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b7a298e4f795a18f3d41262269705afc\\transformed\\browser-1.6.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,266,381", "endColumns": "108,101,114,105", "endOffsets": "159,261,376,482"}, "to": {"startLines": "64,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6638,7197,7299,7414", "endColumns": "108,101,114,105", "endOffsets": "6742,7294,7409,7515"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a43c68922964e2a138ddfb9cf7287a27\\transformed\\core-1.13.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "34,35,36,37,38,39,40,132", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3085,3184,3286,3386,3484,3591,3697,12312", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3179,3281,3381,3479,3586,3692,3812,12408"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4b5fbf6908f3e082e261c13278e081e6\\transformed\\play-services-base-18.5.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,301,463,591,695,852,979,1098,1200,1367,1472,1638,1767,1940,2114,2180,2238", "endColumns": "103,161,127,103,156,126,118,101,166,104,165,128,172,173,65,57,73", "endOffsets": "300,462,590,694,851,978,1097,1199,1366,1471,1637,1766,1939,2113,2179,2237,2311"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4309,4417,4583,4715,4823,4984,5115,5238,5490,5661,5770,5940,6073,6250,6428,6498,6560", "endColumns": "107,165,131,107,160,130,122,105,170,108,169,132,176,177,69,61,77", "endOffsets": "4412,4578,4710,4818,4979,5110,5233,5339,5656,5765,5935,6068,6245,6423,6493,6555,6633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1e754318e29fb6f6717a4ed39daeac16\\transformed\\play-services-basement-18.4.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "141", "endOffsets": "340"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5344", "endColumns": "145", "endOffsets": "5485"}}]}]}