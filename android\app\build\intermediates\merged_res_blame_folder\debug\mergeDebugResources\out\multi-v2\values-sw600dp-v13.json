{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-sw600dp-v13/values-sw600dp-v13.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0218db25d639d0bf937ef580de527e1b\\transformed\\material-1.6.1\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,185,256,328,386,444,553,617,673,732,795", "endLines": "2,3,4,5,6,7,9,10,11,12,13,17", "endColumns": "59,69,70,71,57,57,10,63,55,58,62,10", "endOffsets": "110,180,251,323,381,439,548,612,668,727,790,962"}, "to": {"startLines": "10,11,12,13,14,15,16,18,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "611,671,741,812,884,942,1000,1109,1318,1374,1433,1496", "endLines": "10,11,12,13,14,15,17,18,21,22,23,27", "endColumns": "59,69,70,71,57,57,10,63,55,58,62,10", "endOffsets": "666,736,807,879,937,995,1104,1168,1369,1428,1491,1663"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9255414f3a7f0d9a6be0e99ebb030fdc\\transformed\\firebase-inappmessaging-display-21.0.2\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,128", "endColumns": "72,71", "endOffsets": "123,195"}, "to": {"startLines": "19,20", "startColumns": "4,4", "startOffsets": "1173,1246", "endColumns": "72,71", "endOffsets": "1241,1313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\73718788808fe748888117607a7f7695\\transformed\\appcompat-1.7.0\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,124,193,263,337,413,472,543", "endColumns": "68,68,69,73,75,58,70,67", "endOffsets": "119,188,258,332,408,467,538,606"}}]}]}