import React, { useState } from "react";
import {
    StyleSheet,
    Text,
    View,
    TextInput,
    TouchableOpacity,
    Dimensions,
    ScrollView,
} from "react-native";
import { Colors, Fonts, Default } from "../../constants/styles";
import { useTranslation } from "react-i18next";
import Ionicons from "react-native-vector-icons/Ionicons";
import Feather from "react-native-vector-icons/Feather";
import Loader from "../../components/loader";
import MyStatusBar from "../../components/myStatusBar";
import { useNavigation } from "expo-router";
import { router } from 'expo-router';
import { Picker } from '@react-native-picker/picker';

const { width, height } = Dimensions.get("window");
//const { width } = Dimensions.get("window");


const RegisterScreen = () => {
    const { t } = useTranslation();
    const navigation = useNavigation();
    const [activeTab, setActiveTab] = useState("Entity");
    const [owners, setOwners] = useState([
        {
            name: "<PERSON>",
            role: "Primary Owner",
            emailOtp: "",
            phoneOtp: "",
            confirmed: false,
        },
        {
            name: "<PERSON>",
            role: "Co-Owner",
            emailOtp: "",
            phoneOtp: "",
            confirmed: false,
        },
    ]);

    const handleConfirm = (index) => {
        const updatedOwners = [...owners];
        updatedOwners[index].confirmed = !updatedOwners[index].confirmed;
        setOwners(updatedOwners);
    };

    const navigateToOtpScreen = () => {
        router.push('auth/otpScreen')
        // navigation.push("auth/OtpScreen");

    };

    return (
        <View style={styles.container}>
            <ScrollView showsVerticalScrollIndicator={false}>
                <Text style={styles.headerText}>{t("verifyOwners")}</Text>
                <Text style={styles.subHeaderText}>
                    {t("completeVerificationForAllOwners")}
                </Text>

                {/* Tabs */}
                <View style={styles.tabContainer}>
                    <TouchableOpacity
                        style={[
                            styles.tab,
                            activeTab === "Entity" && styles.activeTab,
                        ]}
                        onPress={() => setActiveTab("Entity")}
                    >
                        <Text
                            style={[
                                styles.tabText,
                                activeTab === "Entity" && styles.activeTabText,
                            ]}
                        >
                            {t("entity")}
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={[
                            styles.tab,
                            activeTab === "Individual" && styles.activeTab,
                        ]}
                        onPress={() => setActiveTab("Individual")}
                    >
                        <Text
                            style={[
                                styles.tabText,
                                activeTab === "Individual" && styles.activeTabText,
                            ]}
                        >
                            {t("individual")}
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Owner Cards */}
                {owners.map((owner, index) => (
                    <View key={index} style={styles.ownerCard}>
                        <View style={styles.ownerHeader}>
                            <Ionicons
                                name="person-circle-outline"
                                size={40}
                                color={Colors.primary}
                            />
                            <View style={styles.ownerInfo}>
                                <Text style={styles.ownerName}>{owner.name}</Text>
                                <Text style={styles.ownerRole}>{owner.role}</Text>
                            </View>
                        </View>

                        <Text style={styles.label}>{t("emailOtp")}</Text>
                        <TextInput
                            value={owner.emailOtp}
                            onChangeText={(text) => {
                                const updatedOwners = [...owners];
                                updatedOwners[index].emailOtp = text;
                                setOwners(updatedOwners);
                            }}
                            placeholder={t("enterOtp")}
                            keyboardType="number-pad"
                            style={styles.input}
                        />

                        <Text style={styles.label}>{t("phoneOtp")}</Text>
                        <TextInput
                            value={owner.phoneOtp}
                            onChangeText={(text) => {
                                const updatedOwners = [...owners];
                                updatedOwners[index].phoneOtp = text;
                                setOwners(updatedOwners);
                            }}
                            placeholder={t("enterOtp")}
                            keyboardType="number-pad"
                            style={styles.input}
                        />

                        <View style={styles.checkboxContainer}>
                            <TouchableOpacity
                                onPress={() => handleConfirm(index)}
                                style={[
                                    styles.checkbox,
                                    owner.confirmed && styles.checkedCheckbox,
                                ]}
                            >
                                {owner.confirmed && (
                                    <Ionicons name="checkmark" size={16} color={Colors.white} />
                                )}
                            </TouchableOpacity>
                            <Text style={styles.checkboxText}>
                                {t("canSign")}
                            </Text>
                        </View>

                    </View>
                ))}

                {/* Proceed Button */}
                <TouchableOpacity
                    style={styles.proceedButton}
                    onPress={navigateToOtpScreen}
                >
                    <Text style={styles.proceedButtonText}>{t("proceed")}</Text>
                </TouchableOpacity>
            </ScrollView>
        </View>
    );
};

export default RegisterScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.lightGreen,
        padding: Default.fixPadding * 2,
    },
    headerText: {
        ...Fonts.Bold20primary,
        textAlign: "center",
        marginBottom: Default.fixPadding,
    },
    subHeaderText: {
        ...Fonts.SemiBold16grey,
        textAlign: "center",
        marginBottom: Default.fixPadding * 2,
    },
    tabContainer: {
        flexDirection: "row",
        justifyContent: "center",
        marginBottom: Default.fixPadding * 2,
    },
    tab: {
        flex: 1,
        paddingVertical: Default.fixPadding,
        alignItems: "center",
        borderRadius: 10,
        backgroundColor: Colors.white,
        marginHorizontal: Default.fixPadding * 0.5,
    },
    activeTab: {
        backgroundColor: Colors.primary,
    },
    tabText: {
        ...Fonts.SemiBold16black,
    },
    activeTabText: {
        color: Colors.white,
    },
    ownerCard: {
        backgroundColor: Colors.white,
        borderRadius: 10,
        padding: Default.fixPadding * 2,
        marginBottom: Default.fixPadding * 2,
        ...Default.shadow,
    },
    ownerHeader: {
        flexDirection: "row",
        alignItems: "center",
        marginBottom: Default.fixPadding * 2,
    },
    ownerInfo: {
        marginLeft: Default.fixPadding,
    },
    ownerName: {
        ...Fonts.Bold16black,
    },
    ownerRole: {
        ...Fonts.SemiBold14grey,
    },
    label: {
        ...Fonts.SemiBold14black,
        marginBottom: Default.fixPadding * 0.5,
    },
    input: {
        borderWidth: 1,
        borderColor: Colors.grey,
        borderRadius: 10,
        padding: Default.fixPadding,
        marginBottom: Default.fixPadding * 1.5,
        ...Fonts.SemiBold14black,
    },
    checkboxContainer: {
        flexDirection: "row",
        alignItems: "center",
        marginBottom: Default.fixPadding,
    },
    checkbox: {
        width: 20,
        height: 20,
        borderWidth: 1,
        borderColor: Colors.grey,
        borderRadius: 5,
        justifyContent: "center",
        alignItems: "center",
        marginRight: Default.fixPadding,
    },
    checkedCheckbox: {
        backgroundColor: Colors.primary,
        borderColor: Colors.primary,
    },
    checkboxText: {
        ...Fonts.SemiBold14black,
    },
    proceedButton: {
        backgroundColor: Colors.primary,
        borderRadius: 10,
        paddingVertical: Default.fixPadding,
        alignItems: "center",
        marginTop: Default.fixPadding * 2,
    },
    proceedButtonText: {
        ...Fonts.Bold18white,
    },
});