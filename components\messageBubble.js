import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Colors, Default, Fonts } from '../constants/styles';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';

const MessageBubble = ({ message, onPress, onLongPress, isRtl }) => {
  const { t } = useTranslation();
  
  const isUserMessage = message.sender === 'user';
  const isSystemMessage = message.sender === 'system';
  
  const getMessageTypeColor = (type) => {
    switch (type) {
      case 'success':
        return Colors.primary;
      case 'warning':
        return '#ff9800';
      case 'error':
        return '#f44336';
      case 'transaction':
        return '#2196f3';
      case 'user':
        return Colors.primary;
      default:
        return Colors.grey;
    }
  };

  const getMessageTypeIcon = (type) => {
    switch (type) {
      case 'success':
        return 'check-circle';
      case 'warning':
        return 'alert-circle';
      case 'error':
        return 'close-circle';
      case 'transaction':
        return 'credit-card';
      case 'user':
        return 'account';
      default:
        return 'information';
    }
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now - date) / (1000 * 60));
      return diffInMinutes < 1 ? 'Just now' : `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      onLongPress={onLongPress}
      style={{
        marginVertical: Default.fixPadding * 0.5,
        marginHorizontal: Default.fixPadding * 2,
        alignSelf: isUserMessage ? (isRtl ? 'flex-start' : 'flex-end') : (isRtl ? 'flex-end' : 'flex-start'),
        maxWidth: '80%',
      }}
    >
      <View
        style={{
          backgroundColor: isUserMessage ? Colors.primary : Colors.white,
          borderRadius: 16,
          padding: Default.fixPadding * 1.5,
          borderWidth: isUserMessage ? 0 : 1,
          borderColor: Colors.lightGrey,
          shadowColor: Colors.black,
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.1,
          shadowRadius: 2,
          elevation: 2,
        }}
      >
        {/* Message Header */}
        {(message.title || !isUserMessage) && (
          <View
            style={{
              flexDirection: isRtl ? 'row-reverse' : 'row',
              alignItems: 'center',
              marginBottom: message.title ? Default.fixPadding * 0.5 : 0,
            }}
          >
            {!isUserMessage && (
              <View
                style={{
                  width: 20,
                  height: 20,
                  borderRadius: 10,
                  backgroundColor: getMessageTypeColor(message.type),
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: isRtl ? 0 : Default.fixPadding * 0.5,
                  marginLeft: isRtl ? Default.fixPadding * 0.5 : 0,
                }}
              >
                <MaterialCommunityIcons
                  name={getMessageTypeIcon(message.type)}
                  size={12}
                  color={Colors.white}
                />
              </View>
            )}
            
            {message.title && (
              <Text
                style={{
                  ...Fonts.SemiBold14black,
                  color: isUserMessage ? Colors.white : Colors.black,
                  flex: 1,
                  textAlign: isRtl ? 'right' : 'left',
                }}
                numberOfLines={2}
              >
                {message.title}
              </Text>
            )}
          </View>
        )}

        {/* Message Body */}
        <Text
          style={{
            ...Fonts.Regular14black,
            color: isUserMessage ? Colors.white : Colors.black,
            textAlign: isRtl ? 'right' : 'left',
            lineHeight: 20,
          }}
        >
          {message.body}
        </Text>

        {/* Message Footer */}
        <View
          style={{
            flexDirection: isRtl ? 'row-reverse' : 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginTop: Default.fixPadding * 0.5,
          }}
        >
          <Text
            style={{
              ...Fonts.Regular12grey,
              color: isUserMessage ? 'rgba(255,255,255,0.7)' : Colors.grey,
              fontSize: 11,
            }}
          >
            {formatTime(message.timestamp)}
          </Text>
          
          {isUserMessage && (
            <MaterialCommunityIcons
              name={message.isRead ? 'check-all' : 'check'}
              size={14}
              color="rgba(255,255,255,0.7)"
              style={{
                marginLeft: isRtl ? 0 : Default.fixPadding * 0.5,
                marginRight: isRtl ? Default.fixPadding * 0.5 : 0,
              }}
            />
          )}
          
          {!message.isRead && !isUserMessage && (
            <View
              style={{
                width: 8,
                height: 8,
                borderRadius: 4,
                backgroundColor: Colors.primary,
                marginLeft: isRtl ? 0 : Default.fixPadding * 0.5,
                marginRight: isRtl ? Default.fixPadding * 0.5 : 0,
              }}
            />
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default MessageBubble;
