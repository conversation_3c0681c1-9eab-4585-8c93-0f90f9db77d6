import { Text, View, FlatList, TouchableOpacity, Image } from "react-native";
import React from "react";
import { useTranslation } from "react-i18next";
import { Colors, Default, Fonts } from "../../constants/styles";
import Ionicons from "react-native-vector-icons/Ionicons";
import MyStatusBar from "../../components/myStatusBar";
import { useNavigation } from "expo-router";

const LatestTransactionScreen = () => {
  const navigation = useNavigation();

  const { t, i18n } = useTranslation();

  const isRtl = i18n.dir() == "rtl";

  function tr(key) {
    return t(`latestTransactionScreen:${key}`);
  }

  const transactionList = [
    {
      key: "1",
      image: require("../../assets/images/Services2.png"),
      name: "<PERSON><PERSON><PERSON> shah",
      other: "Money transfer",
      dollar: "-$140",
      transaction: false,
    },
    {
      key: "2",
      image: require("../../assets/images/transaction1.png"),
      name: "Paypal",
      other: "Deposits",
      dollar: "-$140",
      transaction: true,
    },
    {
      key: "3",
      image: require("../../assets/images/Services10.png"),
      name: "+91 987654321",
      other: "Mobile payment",
      dollar: "-$150",
      transaction: false,
    },
    {
      key: "4",
      image: require("../../assets/images/transaction3.png"),
      name: "Atm",
      other: "Cash withdrawal",
      dollar: "-$140",
      transaction: false,
    },
    {
      key: "5",
      image: require("../../assets/images/Services2.png"),
      name: "Jane Cooper",
      other: "Money transfer",
      dollar: "+$640",
      transaction: true,
    },
    {
      key: "6",
      image: require("../../assets/images/Services5.png"),
      name: "Electricity",
      other: "bill payment",
      dollar: "-$540",
      transaction: false,
    },
    {
      key: "7",
      image: require("../../assets/images/transaction1.png"),
      name: "Paypal",
      other: "Deposits",
      dollar: "-$140",
      transaction: true,
    },
    {
      key: "8",
      image: require("../../assets/images/transaction4.png"),
      name: "eBay",
      other: "Online payment",
      dollar: "-$190",
      transaction: false,
    },
    {
      key: "9",
      image: require("../../assets/images/transaction2.png"),
      name: "Amazon",
      other: "Online payment",
      dollar: "-$440",
      transaction: false,
    },
    {
      key: "10",
      image: require("../../assets/images/transaction3.png"),
      name: "Atm",
      other: "Cash withdrawal",
      dollar: "-$140",
      transaction: false,
    },
    {
      key: "11",
      image: require("../../assets/images/Services10.png"),
      name: "+91 987654321",
      other: "Mobile payment",
      dollar: "-$100",
      transaction: false,
    },
  ];

  const renderItem = ({ item }) => {
    return (
      <View
        style={{
          flexDirection: isRtl ? "row-reverse" : "row",
          justifyContent: "space-between",
          alignItems: "center",
          paddingVertical: Default.fixPadding * 1.2,
          paddingHorizontal: Default.fixPadding * 1.5,
          marginBottom: Default.fixPadding * 2,
          marginHorizontal: Default.fixPadding * 2,
          borderRadius: 10,
          backgroundColor: Colors.white,
          ...Default.shadow,
        }}
      >
        <View
          style={{
            flex: 1,
            flexDirection: isRtl ? "row-reverse" : "row",
            alignItems: "center",
          }}
        >
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
              width: 38,
              height: 38,
              borderRadius: 19,
              backgroundColor: Colors.extraLightGrey,
            }}
          >
            <Image
              resizeMode="contain"
              source={item.image}
              style={{
                width: 22,
                height: 22,
              }}
            />
          </View>
          <View
            style={{
              flex: 1,
              alignItems: isRtl ? "flex-end" : "flex-start",
              paddingHorizontal: Default.fixPadding * 1.5,
            }}
          >
            <Text
              numberOfLines={1}
              style={{ ...Fonts.Bold15black, overflow: "hidden" }}
            >
              {item.name}
            </Text>
            <Text
              numberOfLines={1}
              style={{
                ...Fonts.Bold12grey,
                marginTop: Default.fixPadding * 0.3,
                overflow: "hidden",
              }}
            >
              {item.other}
            </Text>
          </View>
        </View>

        <Text
          numberOfLines={1}
          style={{
            ...(item.transaction === true
              ? Fonts.Bold15green
              : Fonts.Bold15red),
            overflow: "hidden",
            maxWidth: 100,
          }}
        >
          {item.dollar}
        </Text>
      </View>
    );
  };
  return (
    <View style={{ flex: 1, backgroundColor: Colors.regularGrey }}>
      <MyStatusBar />
      <View
        style={{
          flexDirection: isRtl ? "row-reverse" : "row",
          alignItems: "center",
          paddingVertical: Default.fixPadding * 1.2,
          paddingHorizontal: Default.fixPadding * 2,
          backgroundColor: Colors.regularGrey,
          ...Default.shadow,
        }}
      >
        <TouchableOpacity onPress={() => navigation.pop()}>
          <Ionicons
            name={isRtl ? "arrow-forward" : "arrow-back"}
            size={23}
            color={Colors.black}
          />
        </TouchableOpacity>
        <Text
          style={{
            ...Fonts.Bold20black,
            marginHorizontal: Default.fixPadding * 1.5,
          }}
        >
          {tr("transaction")}
        </Text>
      </View>

      <FlatList
        data={transactionList}
        renderItem={renderItem}
        keyExtractor={(item) => item.key}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingTop: Default.fixPadding * 2 }}
      />
    </View>
  );
};

export default LatestTransactionScreen;
