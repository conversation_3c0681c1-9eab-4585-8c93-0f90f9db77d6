{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\73718788808fe748888117607a7f7695\\transformed\\appcompat-1.7.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,377,480,593,678,782,893,971,1048,1139,1232,1324,1418,1518,1611,1706,1802,1893,1984,2065,2172,2276,2374,2477,2581,2685,2842,11891", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "372,475,588,673,777,888,966,1043,1134,1227,1319,1413,1513,1606,1701,1797,1888,1979,2060,2167,2271,2369,2472,2576,2680,2837,2936,11968"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\960e1087eeccdf840de6823a1e7fc27f\\transformed\\ui-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,272,381,479,568,657,747,833,916,996,1080,1154,1235,1310,1385,1463,1529", "endColumns": "89,76,108,97,88,88,89,85,82,79,83,73,80,74,74,77,65,120", "endOffsets": "190,267,376,474,563,652,742,828,911,991,1075,1149,1230,1305,1380,1458,1524,1645"}, "to": {"startLines": "44,45,66,67,68,77,78,127,128,130,131,133,134,135,137,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4045,4135,6640,6749,6847,7625,7714,11479,11565,11727,11807,11973,12047,12128,12274,12521,12599,12665", "endColumns": "89,76,108,97,88,88,89,85,82,79,83,73,80,74,74,77,65,120", "endOffsets": "4130,4207,6744,6842,6931,7709,7799,11560,11643,11802,11886,12042,12123,12198,12344,12594,12660,12781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6f597dc2fa8134c9a491d7aedff8ce\\transformed\\foundation-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "143,144", "startColumns": "4,4", "startOffsets": "12786,12872", "endColumns": "85,84", "endOffsets": "12867,12952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4b5fbf6908f3e082e261c13278e081e6\\transformed\\play-services-base-18.5.0\\res\\values-lo\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,463,589,692,840,961,1065,1177,1325,1426,1588,1713,1880,2037,2101,2166", "endColumns": "103,165,125,102,147,120,103,111,147,100,161,124,166,156,63,64,80", "endOffsets": "296,462,588,691,839,960,1064,1176,1324,1425,1587,1712,1879,2036,2100,2165,2246"}, "to": {"startLines": "47,48,49,50,51,52,53,54,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4288,4396,4566,4696,4803,4955,5080,5188,5436,5588,5693,5859,5988,6159,6320,6388,6457", "endColumns": "107,169,129,106,151,124,107,115,151,104,165,128,170,160,67,68,84", "endOffsets": "4391,4561,4691,4798,4950,5075,5183,5299,5583,5688,5854,5983,6154,6315,6383,6452,6537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0218db25d639d0bf937ef580de527e1b\\transformed\\material-1.6.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,305,420,539,622,688,777,846,905,1000,1065,1123,1188,1249,1309,1415,1476,1536,1594,1665,1784,1870,1952,2065,2140,2216,2306,2373,2439,2508,2582,2661,2734,2811,2880,2950,3035,3110,3203,3296,3370,3439,3533,3585,3652,3736,3820,3882,3946,4009,4108,4200,4295,4387", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,80,114,118,82,65,88,68,58,94,64,57,64,60,59,105,60,59,57,70,118,85,81,112,74,75,89,66,65,68,73,78,72,76,68,69,84,74,92,92,73,68,93,51,66,83,83,61,63,62,98,91,94,91,78", "endOffsets": "219,300,415,534,617,683,772,841,900,995,1060,1118,1183,1244,1304,1410,1471,1531,1589,1660,1779,1865,1947,2060,2135,2211,2301,2368,2434,2503,2577,2656,2729,2806,2875,2945,3030,3105,3198,3291,3365,3434,3528,3580,3647,3731,3815,3877,3941,4004,4103,4195,4290,4382,4461"}, "to": {"startLines": "2,33,41,42,43,69,70,74,79,81,82,83,84,85,86,87,88,89,90,91,92,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2941,3728,3843,3962,6936,7002,7404,7804,7930,8025,8090,8148,8213,8274,8334,8440,8501,8561,8619,8690,8876,8962,9044,9157,9232,9308,9398,9465,9531,9600,9674,9753,9826,9903,9972,10042,10127,10202,10295,10388,10462,10531,10625,10677,10744,10828,10912,10974,11038,11101,11200,11292,11387,11648", "endLines": "5,33,41,42,43,69,70,74,79,81,82,83,84,85,86,87,88,89,90,91,92,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,129", "endColumns": "12,80,114,118,82,65,88,68,58,94,64,57,64,60,59,105,60,59,57,70,118,85,81,112,74,75,89,66,65,68,73,78,72,76,68,69,84,74,92,92,73,68,93,51,66,83,83,61,63,62,98,91,94,91,78", "endOffsets": "269,3017,3838,3957,4040,6997,7086,7468,7858,8020,8085,8143,8208,8269,8329,8435,8496,8556,8614,8685,8804,8957,9039,9152,9227,9303,9393,9460,9526,9595,9669,9748,9821,9898,9967,10037,10122,10197,10290,10383,10457,10526,10620,10672,10739,10823,10907,10969,11033,11096,11195,11287,11382,11474,11722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b7a298e4f795a18f3d41262269705afc\\transformed\\browser-1.6.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,251,366", "endColumns": "97,97,114,99", "endOffsets": "148,246,361,461"}, "to": {"startLines": "65,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6542,7091,7189,7304", "endColumns": "97,97,114,99", "endOffsets": "6635,7184,7299,7399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a43c68922964e2a138ddfb9cf7287a27\\transformed\\core-1.13.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "34,35,36,37,38,39,40,139", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3022,3118,3221,3320,3418,3519,3617,12420", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "3113,3216,3315,3413,3514,3612,3723,12516"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb5951a603c6ef86f8f4868bb3898655\\transformed\\react-android-0.76.9-debug\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,131,201,283,350,417,488", "endColumns": "75,69,81,66,66,70,70", "endOffsets": "126,196,278,345,412,483,554"}, "to": {"startLines": "46,75,76,80,93,136,138", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4212,7473,7543,7863,8809,12203,12349", "endColumns": "75,69,81,66,66,70,70", "endOffsets": "4283,7538,7620,7925,8871,12269,12415"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1e754318e29fb6f6717a4ed39daeac16\\transformed\\play-services-basement-18.4.0\\res\\values-lo\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "55", "startColumns": "4", "startOffsets": "5304", "endColumns": "131", "endOffsets": "5431"}}]}]}