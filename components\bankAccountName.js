import React from "react";
import { Text, View, TouchableOpacity } from "react-native";
import { Colors, Fonts, Default } from "../constants/styles";
import { useTranslation } from "react-i18next";

const BankAccountName = (props) => {
  const { i18n } = useTranslation();

  const isRtl = i18n.dir() == "rtl";

  return (
    <View
      style={{
        borderTopWidth: 1,
        borderTopColor: props.isFirst ? 0 : Colors.lightGrey,
      }}
    >
      <TouchableOpacity
        onPress={() => props.bankAccountNameClickHandler()}
        style={{
          flexDirection: isRtl ? "row-reverse" : "row",
          alignItems: "center",
          padding: Default.fixPadding * 2,
        }}
      >
        <Text style={{ ...Fonts.Bold16black }}>{props.name}</Text>
      </TouchableOpacity>
    </View>
  );
};

export default BankAccountName;
