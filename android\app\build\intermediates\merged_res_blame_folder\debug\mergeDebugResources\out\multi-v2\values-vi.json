{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a43c68922964e2a138ddfb9cf7287a27\\transformed\\core-1.13.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "35,36,37,38,39,40,41,160", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3114,3211,3313,3412,3512,3615,3728,14106", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "3206,3308,3407,3507,3610,3723,3839,14202"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1e754318e29fb6f6717a4ed39daeac16\\transformed\\play-services-basement-18.4.0\\res\\values-vi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "123", "endOffsets": "318"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5412", "endColumns": "127", "endOffsets": "5535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb5951a603c6ef86f8f4868bb3898655\\transformed\\react-android-0.76.9-debug\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,205,277,349,432,503,579,660,743,826,904,981,1053,1137,1220,1297,1373,1448,1538,1611,1690,1764", "endColumns": "72,76,71,71,82,70,75,80,82,82,77,76,71,83,82,76,75,74,89,72,78,73,78", "endOffsets": "123,200,272,344,427,498,574,655,738,821,899,976,1048,1132,1215,1292,1368,1443,1533,1606,1685,1759,1838"}, "to": {"startLines": "33,47,78,85,86,90,103,104,142,143,146,149,150,151,153,154,156,158,159,161,164,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2963,4319,7747,8204,8276,8596,9541,9617,12661,12744,13008,13243,13320,13392,13552,13635,13789,13941,14016,14207,14423,14616,14690", "endColumns": "72,76,71,71,82,70,75,80,82,82,77,76,71,83,82,76,75,74,89,72,78,73,78", "endOffsets": "3031,4391,7814,8271,8354,8662,9612,9693,12739,12822,13081,13315,13387,13471,13630,13707,13860,14011,14101,14275,14497,14685,14764"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b7a298e4f795a18f3d41262269705afc\\transformed\\browser-1.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,283,397", "endColumns": "116,110,113,110", "endOffsets": "167,278,392,503"}, "to": {"startLines": "66,75,76,77", "startColumns": "4,4,4,4", "startOffsets": "6630,7411,7522,7636", "endColumns": "116,110,113,110", "endOffsets": "6742,7517,7631,7742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\73718788808fe748888117607a7f7695\\transformed\\appcompat-1.7.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,377,479,588,672,775,894,972,1048,1139,1232,1327,1421,1521,1614,1709,1803,1894,1985,2069,2173,2281,2382,2487,2602,2707,2864,13086", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "372,474,583,667,770,889,967,1043,1134,1227,1322,1416,1516,1609,1704,1798,1889,1980,2064,2168,2276,2377,2482,2597,2702,2859,2958,13166"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\960e1087eeccdf840de6823a1e7fc27f\\transformed\\ui-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,393,493,585,670,763,857,938,1028,1119,1191,1267,1344,1420,1497,1563", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,75,76,75,76,65,113", "endOffsets": "196,282,388,488,580,665,758,852,933,1023,1114,1186,1262,1339,1415,1492,1558,1672"}, "to": {"startLines": "45,46,70,71,72,87,88,138,139,144,145,148,152,155,157,162,163,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4137,4233,6956,7062,7162,8359,8444,12344,12438,12827,12917,13171,13476,13712,13865,14280,14357,14502", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,75,76,75,76,65,113", "endOffsets": "4228,4314,7057,7157,7249,8439,8532,12433,12514,12912,13003,13238,13547,13784,13936,14352,14418,14611"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6f597dc2fa8134c9a491d7aedff8ce\\transformed\\foundation-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,86", "endOffsets": "138,225"}, "to": {"startLines": "168,169", "startColumns": "4,4", "startOffsets": "14769,14857", "endColumns": "87,86", "endOffsets": "14852,14939"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0218db25d639d0bf937ef580de527e1b\\transformed\\material-1.6.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,298,397,511,591,654,748,822,881,967,1028,1086,1150,1211,1265,1382,1439,1499,1553,1628,1755,1839,1917,2017,2101,2179,2270,2337,2403,2471,2547,2628,2707,2782,2855,2931,3020,3097,3188,3282,3356,3426,3519,3568,3634,3719,3805,3867,3931,3994,4093,4198,4296,4401", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,98,113,79,62,93,73,58,85,60,57,63,60,53,116,56,59,53,74,126,83,77,99,83,77,90,66,65,67,75,80,78,74,72,75,88,76,90,93,73,69,92,48,65,84,85,61,63,62,98,104,97,104,77", "endOffsets": "215,293,392,506,586,649,743,817,876,962,1023,1081,1145,1206,1260,1377,1434,1494,1548,1623,1750,1834,1912,2012,2096,2174,2265,2332,2398,2466,2542,2623,2702,2777,2850,2926,3015,3092,3183,3277,3351,3421,3514,3563,3629,3714,3800,3862,3926,3989,4088,4193,4291,4396,4474"}, "to": {"startLines": "2,34,42,43,44,73,74,84,89,91,92,93,94,95,96,97,98,99,100,101,102,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3036,3844,3943,4057,7254,7317,8130,8537,8667,8753,8814,8872,8936,8997,9051,9168,9225,9285,9339,9414,9698,9782,9860,9960,10044,10122,10213,10280,10346,10414,10490,10571,10650,10725,10798,10874,10963,11040,11131,11225,11299,11369,11462,11511,11577,11662,11748,11810,11874,11937,12036,12141,12239,12519", "endLines": "5,34,42,43,44,73,74,84,89,91,92,93,94,95,96,97,98,99,100,101,102,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,140", "endColumns": "12,77,98,113,79,62,93,73,58,85,60,57,63,60,53,116,56,59,53,74,126,83,77,99,83,77,90,66,65,67,75,80,78,74,72,75,88,76,90,93,73,69,92,48,65,84,85,61,63,62,98,104,97,104,77", "endOffsets": "265,3109,3938,4052,4132,7312,7406,8199,8591,8748,8809,8867,8931,8992,9046,9163,9220,9280,9334,9409,9536,9777,9855,9955,10039,10117,10208,10275,10341,10409,10485,10566,10645,10720,10793,10869,10958,11035,11126,11220,11294,11364,11457,11506,11572,11657,11743,11805,11869,11932,12031,12136,12234,12339,12592"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4b5fbf6908f3e082e261c13278e081e6\\transformed\\play-services-base-18.5.0\\res\\values-vi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,449,570,675,836,962,1077,1177,1346,1449,1602,1728,1883,2028,2092,2152", "endColumns": "97,157,120,104,160,125,114,99,168,102,152,125,154,144,63,59,78", "endOffsets": "290,448,569,674,835,961,1076,1176,1345,1448,1601,1727,1882,2027,2091,2151,2230"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4396,4498,4660,4785,4894,5059,5189,5308,5540,5713,5820,5977,6107,6266,6415,6483,6547", "endColumns": "101,161,124,108,164,129,118,103,172,106,156,129,158,148,67,63,82", "endOffsets": "4493,4655,4780,4889,5054,5184,5303,5407,5708,5815,5972,6102,6261,6410,6478,6542,6625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\479cab5183b5be1afec009352c628583\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,211,264,307,380,449,523,575", "endColumns": "108,46,52,42,72,68,73,51,63", "endOffsets": "159,206,259,302,375,444,518,570,634"}, "to": {"startLines": "67,68,69,79,80,81,82,83,141", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6747,6856,6903,7819,7862,7935,8004,8078,12597", "endColumns": "108,46,52,42,72,68,73,51,63", "endOffsets": "6851,6898,6951,7857,7930,7999,8073,8125,12656"}}]}]}