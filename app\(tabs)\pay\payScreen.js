{/*import {
    Text,
    View,
    <PERSON><PERSON>View,
    FlatList,
    Image,
    TouchableOpacity,
    Dimensions,
    ImageBackground,
} from "react-native";
import React from "react";
import { useTranslation } from "react-i18next";
import { Colors, Default, Fonts } from "../../../constants/styles";
import MyStatusBar from "../../../components/myStatusBar";
import { useNavigation } from "expo-router";
import Ionicons from "react-native-vector-icons/Ionicons";

const { width } = Dimensions.get("window");

const PayScreen = () => {
    const navigation = useNavigation();

    const { t, i18n } = useTranslation();

    const isRtl = i18n.dir() == "rtl";

    function tr(key) {
        return t(`payScreen:${key}`);
    }

    const recentTransactions = [
        {
            key: "1",
            title: "Baduu Investment",
            description: "Payment of Soap & Detergents",
            date: "02/02/25",
            amount: "20,000,000",
        },
        // Add more transactions here
    ];

    const renderTransactionItem = ({ item }) => {
        return (
            <View
                key={item.key}
                style={{
                    flexDirection: isRtl ? "row-reverse" : "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: Default.fixPadding,
                    borderBottomWidth: 1,
                    borderBottomColor: Colors.lightGrey,
                }}
            >
                <View style={{ flex: 1 }}>
                    <Text style={{ ...Fonts.Bold16black }}>{item.title}</Text>
                    <Text style={{ ...Fonts.SemiBold14grey }}>{item.description}</Text>
                    <Text style={{ ...Fonts.SemiBold14grey }}>{item.date}</Text>
                </View>
                <View style={{ alignItems: isRtl ? "flex-start" : "flex-end" }}>
                    <Text style={{ ...Fonts.Bold16primary }}>{item.amount}</Text>
                    <TouchableOpacity
                        onPress={() => navigation.push("transactionDetailScreen", { transactionId: item.key })}
                    >
                        <Text style={{ ...Fonts.SemiBold14green }}>{tr("viewDetails")}</Text>
                    </TouchableOpacity>
                </View>
            </View>
        );
    };

    const [activeTab, setActiveTab] = React.useState("default");

    const paymentApprovals = [
        {
            key: "1",
            title: "John Doe",
            description: "Approval for payment of office supplies",
            date: "02/02/25",
            amount: "5,000,000",
        },
        // Add more approvals here
    ];

    const renderApprovalItem = ({ item }) => {
        return (
            <View
                key={item.key}
                style={{
                    flexDirection: isRtl ? "row-reverse" : "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: Default.fixPadding,
                    borderBottomWidth: 1,
                    borderBottomColor: Colors.lightGrey,
                    borderWidth: 0,
                    borderRadius: 10,
                    backgroundColor: Colors.white
                }}
            >
                <View style={{ flex: 1, }}>
                    <Text style={{ ...Fonts.Bold16black }}>{item.title}</Text>
                    <Text style={{ ...Fonts.SemiBold14grey }}>{item.description}</Text>
                    <Text style={{ ...Fonts.SemiBold14grey }}>{item.date}</Text>
                </View>
                <View style={{
                    flexDirection: "row",
                    alignItems: "center",

                }}>
                    <TouchableOpacity
                        onPress={() => navigation.push("approvalDetailScreen", { approvalId: item.key })}
                        style={{ marginHorizontal: Default.fixPadding * 0.5, backgroundColor: Colors.green, padding: 1, borderRadius: 5 }}
                    >
                        <Ionicons
                            name="checkmark-outline"
                            size={25}
                            color={Colors.white}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => {
                            // Handle delete action
                        }}
                        style={{ marginHorizontal: Default.fixPadding * 0.5, backgroundColor: Colors.red, padding: 1, borderRadius: 5 }}
                    >
                        <Ionicons
                            name="trash-outline"
                            size={25}
                            color={Colors.white}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        );
    };

    return (
        <View style={{ flex: 1, backgroundColor: Colors.regularGrey }}>
            <MyStatusBar />
            <ImageBackground
                source={require("../../../assets/images/depositImage.png")}
                style={{
                    width: width,
                    height: 80,
                    justifyContent: "center",
                    alignItems: "center",
                    paddingHorizontal: Default.fixPadding * 2,
                }}
            >
                <Text style={{ ...Fonts.ExtraBold20white }}>
                    {tr("payApprovals")}
                </Text>
            </ImageBackground>

            <View style={{ flexDirection: "row", justifyContent: "space-around", marginVertical: Default.fixPadding * 2 }}>
                <TouchableOpacity
                    style={{
                        backgroundColor: activeTab === "default" ? Colors.primary : Colors.lightGrey,
                        padding: Default.fixPadding,
                        borderRadius: 5,
                        flex: 1,
                        marginHorizontal: Default.fixPadding * 0.5,
                        alignItems: "center",
                    }}
                    onPress={() => setActiveTab("default")}
                >
                    <Text style={{ ...Fonts.Bold16white }}>{tr("quickPayment")}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={{
                        backgroundColor: activeTab === "approvals" ? Colors.primary : Colors.lightGrey,
                        padding: Default.fixPadding,
                        borderRadius: 5,
                        flex: 1,
                        marginHorizontal: Default.fixPadding * 0.5,
                        alignItems: "center",
                    }}
                    onPress={() => setActiveTab("approvals")}
                >
                    <Text style={{ ...Fonts.Bold16white }}>{tr("paymentApprovals")}</Text>
                    {paymentApprovals.length > 0 && (
                        <View
                            style={{
                                position: "absolute",
                                top: -5,
                                right: -2,
                                backgroundColor: Colors.red,
                                borderRadius: 10,
                                paddingHorizontal: 6,
                                paddingVertical: 2,
                            }}
                        >
                            <Text style={{ ...Fonts.Bold12white }}>{paymentApprovals.length}</Text>
                        </View>
                    )}
                </TouchableOpacity>
            </View>

            {activeTab === "default" ? (
                <ScrollView showsVerticalScrollIndicator={false}>
                    <View style={{
                        alignItems: "center",
                        marginVertical: Default.fixPadding * 2,
                        height: 250,
                        flex: 1,
                        paddingVertical: Default.fixPadding * 2,
                        paddingHorizontal: Default.fixPadding * 0.5,
                        marginHorizontal: Default.fixPadding,
                        marginBottom: Default.fixPadding * 2,
                        textAlign: "center",
                        justifyContent: "center",
                        borderColor: Colors.black,
                        borderWidth: 1,
                        borderRadius: 10,
                        backgroundColor: Colors.white,
                    }}>
                        <Text style={{ ...Fonts.Bold16black }}>{tr("scanQRCode")}</Text>
                    </View>

                    <View style={{ flexDirection: "row", justifyContent: "space-around", marginVertical: Default.fixPadding }}>
                        <TouchableOpacity
                            style={{
                                backgroundColor: Colors.primary,
                                padding: Default.fixPadding,
                                borderRadius: 5,
                                flex: 1,
                                marginHorizontal: Default.fixPadding * 0.5,
                                alignItems: "center",
                            }}
                            onPress={() => {
                                // Handle upload QR code from image
                            }}
                        >
                            <Text style={{ ...Fonts.Bold16white }}>{tr("uploadQRCode")}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={{
                                backgroundColor: Colors.primary,
                                padding: Default.fixPadding,
                                borderRadius: 5,
                                flex: 1,
                                marginHorizontal: Default.fixPadding * 0.5,
                                alignItems: "center",
                            }}
                            onPress={() => {
                                // Handle enter Merchant ID
                            }}
                        >
                            <Text style={{ ...Fonts.Bold16white }}>{tr("enterMerchantID")}</Text>
                        </TouchableOpacity>
                    </View>

                    <View style={{ marginHorizontal: Default.fixPadding * 2 }}>
                        <Text style={{ ...Fonts.Bold18black, marginBottom: Default.fixPadding }}>
                            {tr("recentTransactions")}
                        </Text>
                        <FlatList
                            data={recentTransactions}
                            renderItem={renderTransactionItem}
                            keyExtractor={(item) => item.key}
                            showsVerticalScrollIndicator={false}
                        />
                    </View>
                </ScrollView>
            ) : (
                <ScrollView showsVerticalScrollIndicator={false}>
                    <View style={{ marginHorizontal: Default.fixPadding * 2 }}>
                        <Text style={{ ...Fonts.Bold18black, marginBottom: Default.fixPadding }}>
                            {tr("pendingApprovals")}
                        </Text>
                        <FlatList
                            data={paymentApprovals}
                            renderItem={renderApprovalItem}
                            keyExtractor={(item) => item.key}
                            showsVerticalScrollIndicator={false}
                        />
                    </View>
                </ScrollView>
            )}
        </View>
    );
};

export default PayScreen;*/}
//Squatting fundTransferScreen in this tab for now
import {
  Text,
  View,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  ScrollView,
  TextInput,
  Platform,
} from "react-native";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Colors, Default, Fonts } from "../../../constants/styles";
import Ionicons from "react-native-vector-icons/Ionicons";
import MyStatusBar from "../../../components/myStatusBar";
import { useNavigation } from "expo-router";
import SelectAccountNo from "../../../components/selectAccountNo";
import { BottomSheet } from "react-native-btr";
import SelectBankName from "../../../components/selectBankName";

const PayIMPSTab = () => {
  const { t, i18n } = useTranslation();

  const isRtl = i18n.dir() == "rtl";

  function tr(key) {
    return t(`payIMPSScreen:${key}`);
  }

  const [fromAccountNoBottomSheet, setFromAccountNoBottomSheet] = useState();

  const toggleCloseAccountNumber = () => {
    setFromAccountNoBottomSheet(!fromAccountNoBottomSheet);
  };

  const fromAccountNoList = [
    {
      key: "1",
      name: tr("savingAccount"),
      no: "SB-*******1231",
    },
    {
      key: "2",
      name: tr("currentAccount"),
      no: "SB-*******1232",
    },
    {
      key: "3",
      name: tr("salaryAccount"),
      no: "SB-*******1233",
    },
    {
      key: "4",
      name: tr("nriAccount"),
      no: "SB-*******1234",
    },
  ];
  const [selectFromAccountNo, setSelectFromAccountNo] = useState();

  const [bankNameBottomSheet, setBankNameBottomSheet] = useState();
  const toggleCloseSelectBank = () => {
    setBankNameBottomSheet(!bankNameBottomSheet);
  };

  const bankNameList = [
    {
      key: "1",
      name: "Amana bank",
    },
    {
      key: "2",
      name: "Mkombozi bank",
    },
    {
      key: "3",
      name: "TCB bank",
    },
    {
      key: "4",
      name: "Ecobank bank",
    },
    {
      key: "5",
      name: "NMB bank",
    },
    {
      key: "6",
      name: "CRDB bank",
    },
  ];
  const [bankName, setBankName] = useState();

  const [bankHolderName, setBankHolderName] = useState();
  const [toAccountNo, setToAccountNo] = useState();
  const [code, setCode] = useState();
  const [amount, setAmount] = useState();

  return (
    <View style={{ flex: 1, backgroundColor: Colors.regularGrey }}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        automaticallyAdjustKeyboardInsets={true}
      >
        <Text
          style={{
            textAlign: isRtl ? "right" : "left",
            ...Fonts.Bold17black,
            marginTop: Default.fixPadding,
            marginHorizontal: Default.fixPadding * 2,
          }}
        >
          {tr("fromAccount")}
        </Text>

        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() => setFromAccountNoBottomSheet(true)}
          style={{
            paddingHorizontal: Default.fixPadding * 1.5,
            paddingVertical:
              Platform.OS === "ios"
                ? Default.fixPadding * 1.2
                : Default.fixPadding * 1.5,
            marginTop: Default.fixPadding * 1.2,
            marginBottom: Default.fixPadding * 2,
            marginHorizontal: Default.fixPadding * 2,
            borderRadius: 10,
            backgroundColor: Colors.white,
            ...Default.shadow,
          }}
        >
          <Text
            style={{
              ...(selectFromAccountNo
                ? Fonts.SemiBold15black
                : Fonts.SemiBold15grey),
              textAlign: isRtl ? "right" : "left",
            }}
          >
            {selectFromAccountNo ? selectFromAccountNo : tr("accountNo")}
          </Text>
        </TouchableOpacity>

        <Text
          style={{
            textAlign: isRtl ? "right" : "left",
            ...Fonts.Bold17black,
            marginHorizontal: Default.fixPadding * 2,
          }}
        >
          {tr("mnoName")}
        </Text>

        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() => setBankNameBottomSheet(true)}
          style={{
            paddingHorizontal: Default.fixPadding * 1.5,
            paddingVertical:
              Platform.OS === "ios"
                ? Default.fixPadding * 1.2
                : Default.fixPadding * 1.5,
            marginTop: Default.fixPadding,
            marginBottom: Default.fixPadding * 2,
            marginHorizontal: Default.fixPadding * 2,
            borderRadius: 10,
            backgroundColor: Colors.white,
            ...Default.shadow,
          }}
        >
          <Text
            style={{
              ...(bankName ? Fonts.SemiBold15black : Fonts.SemiBold15grey),
              textAlign: isRtl ? "right" : "left",
            }}
          >
            {bankName ? bankName : tr("mobileOperator")}
          </Text>
        </TouchableOpacity>
        <Text
          style={{
            textAlign: isRtl ? "right" : "left",
            ...Fonts.Bold17black,
            marginHorizontal: Default.fixPadding * 2,
          }}
        >
          {tr("toAccount")}
        </Text>

        <View style={{ marginTop: Default.fixPadding, ...styles.textInput }}>
          <TextInput
            value={toAccountNo}
            onChangeText={setToAccountNo}
            keyboardType="number-pad"
            placeholder={tr("enterAccountNo")}
            placeholderTextColor={Colors.grey}
            selectionColor={Colors.primary}
            numberOfLines={1}
            style={{
              ...Fonts.SemiBold15black,
              padding: 0,
              textAlign: isRtl ? "right" : "left",
            }}
          />
        </View>

        <Text
          style={{
            textAlign: isRtl ? "right" : "left",
            ...Fonts.Bold17black,
            marginHorizontal: Default.fixPadding * 2,
          }}
        >
          {tr("amount")}
        </Text>

        <View style={{ marginTop: Default.fixPadding, ...styles.textInput }}>
          <TextInput
            value={amount}
            onChangeText={setAmount}
            keyboardType="number-pad"
            placeholder={tr("enterAmount")}
            placeholderTextColor={Colors.grey}
            selectionColor={Colors.primary}
            numberOfLines={1}
            style={{
              ...Fonts.SemiBold15black,
              padding: 0,
              textAlign: isRtl ? "right" : "left",
            }}
          />
        </View>
      </ScrollView>

      <BottomSheet
        visible={fromAccountNoBottomSheet}
        onBackButtonPress={toggleCloseAccountNumber}
        onBackdropPress={toggleCloseAccountNumber}
      >
        <View
          style={{ ...styles.bottomSheet, paddingTop: Default.fixPadding * 2 }}
        >
          {fromAccountNoList.map((item) => {
            return (
              <SelectAccountNo
                key={item.key}
                name={item.name}
                no={item.no}
                accountNoCardClick={() => {
                  setSelectFromAccountNo(item.no);
                  toggleCloseAccountNumber();
                }}
                selected={selectFromAccountNo === item.no}
              />
            );
          })}
        </View>
      </BottomSheet>

      <BottomSheet
        visible={bankNameBottomSheet}
        onBackButtonPress={toggleCloseSelectBank}
        onBackdropPress={toggleCloseSelectBank}
      >
        <View style={styles.bottomSheet}>
          {bankNameList.map((item, index) => {
            return (
              <SelectBankName
                key={item.key}
                name={item.name}
                isFirst={index == 0}
                bankNameClickHandler={() => {
                  setBankName(item.name);
                  toggleCloseSelectBank();
                }}
              />
            );
          })}
        </View>
      </BottomSheet>
    </View>
  );
};

const PaymentIBANTab = () => {
  const { t, i18n } = useTranslation();

  const isRtl = i18n.dir() == "rtl";

  function tr(key) {
    return t(`paymentIBANScreen:${key}`);
  }

  const [fromAccountNoBottomSheet, setFromAccountNoBottomSheet] = useState();

  const toggleCloseAccountNumber = () => {
    setFromAccountNoBottomSheet(!fromAccountNoBottomSheet);
  };

  const fromAccountNoList = [
    {
      key: "1",
      name: tr("savingAccount"),
      no: "SB-*******1231",
    },
    {
      key: "2",
      name: tr("currentAccount"),
      no: "SB-*******1232",
    },
    {
      key: "3",
      name: tr("salaryAccount"),
      no: "SB-*******1233",
    },
    {
      key: "4",
      name: tr("nriAccount"),
      no: "SB-*******1234",
    },
  ];
  const [fromAccountNo, setFromAccountNo] = useState();

  const [number, setNumber] = useState();
  const [name, setName] = useState();
  const [code, setCode] = useState();

  const [beneficiaryBankBottomSheet, setBeneficiaryBankBottomSheet] =
    useState();

  const toggleCloseBeneficiaryBank = () => {
    setBeneficiaryBankBottomSheet(!beneficiaryBankBottomSheet);
  };

  const beneficiaryBankList = [
    {
      key: "1",
      name: "Exim bank",
    },
    {
      key: "2",
      name: "TCB bank",
    },
    {
      key: "3",
      name: "Mkombozi bank",
    },
    {
      key: "4",
      name: "Ecobank bank",
    },
    {
      key: "5",
      name: "NMB bank",
    },
    {
      key: "6",
      name: "CRDB bank",
    },
  ];
  const [selectBeneficiaryBank, setSelectBeneficiaryBank] = useState();

  const [amount, setAmount] = useState();
  const [remark, setRemark] = useState();

  return (
    <View style={{ flex: 1, backgroundColor: Colors.regularGrey }}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        automaticallyAdjustKeyboardInsets={true}
      >
        <Text
          style={{
            textAlign: isRtl ? "right" : "left",
            ...Fonts.Bold17black,
            marginTop: Default.fixPadding,
            marginHorizontal: Default.fixPadding * 2,
          }}
        >
          {tr("fromAccount")}
        </Text>

        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() => setFromAccountNoBottomSheet(true)}
          style={{
            paddingHorizontal: Default.fixPadding * 1.5,
            paddingVertical:
              Platform.OS === "ios"
                ? Default.fixPadding * 1.2
                : Default.fixPadding * 1.5,
            marginTop: Default.fixPadding * 1.2,
            marginBottom: Default.fixPadding * 2,
            marginHorizontal: Default.fixPadding * 2,
            borderRadius: 10,
            backgroundColor: Colors.white,
            ...Default.shadow,
          }}
        >
          <Text
            style={{
              ...(fromAccountNo ? Fonts.SemiBold15black : Fonts.SemiBold15grey),
              textAlign: isRtl ? "right" : "left",
            }}
          >
            {fromAccountNo ? fromAccountNo : "SB - ***********"}
          </Text>
        </TouchableOpacity>

        <Text
          style={{
            textAlign: isRtl ? "right" : "left",
            ...Fonts.Bold17black,
            marginHorizontal: Default.fixPadding * 2,
          }}
        >
          {tr("beneficiaryInfo")}
        </Text>





        <TouchableOpacity
          onPress={() => setBeneficiaryBankBottomSheet(true)}
          style={{
            paddingHorizontal: Default.fixPadding * 1.5,
            paddingVertical:
              Platform.OS === "ios"
                ? Default.fixPadding * 1.2
                : Default.fixPadding * 1.5,
            marginBottom: Default.fixPadding * 2,
            marginHorizontal: Default.fixPadding * 2,
            borderRadius: 10,
            backgroundColor: Colors.white,
            ...Default.shadow,
          }}
        >
          <Text
            style={{
              ...(selectBeneficiaryBank
                ? Fonts.SemiBold15black
                : Fonts.SemiBold15grey),
              textAlign: isRtl ? "right" : "left",
            }}
          >
            {selectBeneficiaryBank ? selectBeneficiaryBank : tr("bank")}
          </Text>
        </TouchableOpacity>
        <View style={{ marginTop: Default.fixPadding, ...styles.textInput }}>
          <TextInput
            value={number}
            onChangeText={setNumber}
            placeholder={tr("number")}
            keyboardType="number-pad"
            placeholderTextColor={Colors.grey}
            selectionColor={Colors.primary}
            numberOfLines={1}
            style={{
              ...Fonts.SemiBold15black,
              padding: 0,
              textAlign: isRtl ? "right" : "left",
            }}
          />
        </View>
        <View style={{ ...styles.textInput }}>
          <TextInput
            value={amount}
            onChangeText={setAmount}
            keyboardType="number-pad"
            placeholder={tr("amount")}
            placeholderTextColor={Colors.grey}
            selectionColor={Colors.primary}
            numberOfLines={1}
            style={{
              padding: 0,
              ...Fonts.SemiBold15black,
              textAlign: isRtl ? "right" : "left",
            }}
          />
        </View>
        <View style={{ ...styles.textInput }}>
          <TextInput
            value={remark}
            onChangeText={setRemark}
            placeholder={tr("remark")}
            placeholderTextColor={Colors.grey}
            selectionColor={Colors.primary}
            numberOfLines={1}
            style={{
              ...Fonts.SemiBold15black,
              padding: 0,
              textAlign: isRtl ? "right" : "left",
            }}
          />
        </View>
      </ScrollView>

      <BottomSheet
        visible={fromAccountNoBottomSheet}
        onBackButtonPress={toggleCloseAccountNumber}
        onBackdropPress={toggleCloseAccountNumber}
      >
        <View
          style={{ ...styles.bottomSheet, paddingTop: Default.fixPadding * 2 }}
        >
          {fromAccountNoList.map((item) => {
            return (
              <SelectAccountNo
                key={item.key}
                name={item.name}
                no={item.no}
                accountNoCardClick={() => {
                  setFromAccountNo(item.no);
                  toggleCloseAccountNumber();
                }}
                selected={fromAccountNo === item.no}
              />
            );
          })}
        </View>
      </BottomSheet>

      <BottomSheet
        visible={beneficiaryBankBottomSheet}
        onBackButtonPress={toggleCloseBeneficiaryBank}
        onBackdropPress={toggleCloseBeneficiaryBank}
      >
        <View style={styles.bottomSheet}>
          {beneficiaryBankList.map((item, index) => {
            return (
              <SelectBankName
                key={item.key}
                name={item.name}
                isFirst={index == 0}
                bankNameClickHandler={() => {
                  setSelectBeneficiaryBank(item.name);
                  toggleCloseBeneficiaryBank();
                }}
              />
            );
          })}
        </View>
      </BottomSheet>
    </View>
  );
};

const BeneficiaryPayTab = () => {
  const { t, i18n } = useTranslation();

  const isRtl = i18n.dir() == "rtl";

  function tr(key) {
    return t(`beneficiaryPayScreen:${key}`);
  }

  const [accountNumberBottomSheet, setAccountNumberBottomSheet] = useState();

  const toggleCloseAccountNumber = () => {
    setAccountNumberBottomSheet(!accountNumberBottomSheet);
  };

  const accountList = [
    {
      key: "1",
      name: tr("savingAccount"),
      no: "SB-*******1231",
    },
    {
      key: "2",
      name: tr("currentAccount"),
      no: "SB-*******1232",
    },
    {
      key: "3",
      name: tr("salaryAccount"),
      no: "SB-*******1233",
    },
    {
      key: "4",
      name: tr("nriAccount"),
      no: "SB-*******1234",
    },
  ];

  const [selectAccount, setSelectAccount] = useState();

  const [name, setName] = useState();
  const [accountNumber, setAccountNumber] = useState();
  const [amount, setAmount] = useState();
  const [transLimit, setTransferLimit] = useState();

  const [bankNameBottomSheet, setBankNameBottomSheet] = useState();

  const toggleCloseSelectBank = () => {
    setBankNameBottomSheet(!bankNameBottomSheet);
  };

  const [bankName, setBankName] = useState();

  const bankNameList = [
    {
      key: "1",
      name: "Equity bank",
    },
    {
      key: "2",
      name: "Mkombozi bank",
    },
    {
      key: "3",
      name: "Exim bank",
    },
    {
      key: "4",
      name: "Ecobank bank",
    },
    {
      key: "5",
      name: "NMN bank",
    },
    {
      key: "6",
      name: "CRDB bank",
    },
  ];

  return (
    <View style={{ flex: 1, backgroundColor: Colors.regularGrey }}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        automaticallyAdjustKeyboardInsets={true}
      >
        <Text
          style={{
            textAlign: isRtl ? "right" : "left",
            ...Fonts.Bold17black,
            marginTop: Default.fixPadding,
            marginHorizontal: Default.fixPadding * 2,
          }}
        >
          {tr("fromAccount")}
        </Text>

        <TouchableOpacity
          onPress={() => setAccountNumberBottomSheet(true)}
          style={{
            paddingHorizontal: Default.fixPadding * 1.5,
            paddingVertical:
              Platform.OS === "ios"
                ? Default.fixPadding * 1.2
                : Default.fixPadding * 1.5,
            marginTop: Default.fixPadding * 1.2,
            marginBottom: Default.fixPadding * 2,
            marginHorizontal: Default.fixPadding * 2,
            borderRadius: 10,
            backgroundColor: Colors.white,
            ...Default.shadow,
          }}
        >
          <Text
            style={{
              ...(selectAccount ? Fonts.SemiBold15black : Fonts.SemiBold15grey),
              textAlign: isRtl ? "right" : "left",
            }}
          >
            {selectAccount ? selectAccount : "SB - ***********"}
          </Text>
        </TouchableOpacity>
        <Text
          style={{
            textAlign: isRtl ? "right" : "left",
            ...Fonts.Bold17black,
            marginHorizontal: Default.fixPadding * 2,
          }}
        >
          {tr("beneficiaryInfo")}
        </Text>


        <View style={{ ...styles.textInput }}>
          <TextInput
            value={accountNumber}
            onChangeText={setAccountNumber}
            placeholder={tr("accountNumber")}
            keyboardType="number-pad"
            placeholderTextColor={Colors.grey}
            selectionColor={Colors.primary}
            numberOfLines={1}
            style={{
              ...Fonts.SemiBold15black,
              padding: 0,
              textAlign: isRtl ? "right" : "left",
            }}
          />
        </View>
        <View style={{ ...styles.textInput }}>
          <TextInput
            value={amount}
            onChangeText={setAmount}
            placeholder={tr("amount")}
            keyboardType="number-pad"
            placeholderTextColor={Colors.grey}
            selectionColor={Colors.primary}
            numberOfLines={1}
            style={{
              ...Fonts.SemiBold15black,
              padding: 0,
              textAlign: isRtl ? "right" : "left",
            }}
          />
        </View>

      </ScrollView>

      <BottomSheet
        visible={accountNumberBottomSheet}
        onBackButtonPress={toggleCloseAccountNumber}
        onBackdropPress={toggleCloseAccountNumber}
      >
        <View
          style={{ ...styles.bottomSheet, paddingTop: Default.fixPadding * 2 }}
        >
          {accountList.map((item) => {
            return (
              <SelectAccountNo
                key={item.key}
                name={item.name}
                no={item.no}
                accountNoCardClick={() => {
                  setSelectAccount(item.no);
                  toggleCloseAccountNumber();
                }}
                selected={selectAccount === item.no}
              />
            );
          })}
        </View>
      </BottomSheet>

      <BottomSheet
        visible={bankNameBottomSheet}
        onBackButtonPress={toggleCloseSelectBank}
        onBackdropPress={toggleCloseSelectBank}
      >
        <View style={styles.bottomSheet}>
          {bankNameList.map((item, index) => {
            return (
              <SelectBankName
                key={item.key}
                name={item.name}
                isFirst={index == 0}
                bankNameClickHandler={() => {
                  setBankName(item.name);
                  toggleCloseSelectBank();
                }}
              />
            );
          })}
        </View>
      </BottomSheet>
    </View>
  );
};

const FundTransferScreen = () => {
  const navigation = useNavigation();

  const { t, i18n } = useTranslation();

  const isRtl = i18n.dir() == "rtl";

  function tr(key) {
    return t(`fundTransferScreen:${key}`);
  }

  const fundTransferList = [
    {
      key: "1",
      title: tr("safariBank"),
    },
    {
      key: "2",
      title: tr("otherBank"),
    },
    {
      key: "3",
      title: tr("mobile"),
    },
  ];

  const [fundTransfer, setFundTransfer] = useState(tr("beneficiaryPay"));

  const renderItemFundTransfer = ({ item }) => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => setFundTransfer(item.title)}
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          paddingVertical: Default.fixPadding * 0.8,
          paddingHorizontal: Default.fixPadding * 0.4,
          marginHorizontal: Default.fixPadding,
          marginTop: Default.fixPadding * 2,
          marginBottom: Default.fixPadding,
          borderRadius: 10,
          backgroundColor:
            fundTransfer === item.title ? Colors.primary : Colors.white,
          ...Default.shadow,
        }}
      >
        <Text
          numberOfLines={2}
          style={{
            ...(fundTransfer === item.title
              ? Fonts.Bold16white
              : Fonts.Bold16grey),
            textAlign: "center",
            overflow: "hidden",
          }}
        >
          {item.title}
        </Text>
      </TouchableOpacity>
    );
  };

  const ListFooterComponent = () => {
    if (fundTransfer === tr("safariBank")) {
      return <BeneficiaryPayTab />;
    } else if (fundTransfer === tr("otherBanks")) {
      return <PaymentIBANTab />;
    } else if (fundTransfer === tr("mobile")) {
      return <PayIMPSTab />;
    } else {
      return <BeneficiaryPayTab />;
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <MyStatusBar />
      <View style={{ flex: 1, backgroundColor: Colors.regularGrey }}>
        <View
          style={{
            flexDirection: isRtl ? "row-reverse" : "row",
            alignItems: "center",
            paddingVertical: Default.fixPadding * 1.2,
            paddingHorizontal: Default.fixPadding * 2,
            backgroundColor: Colors.regularGrey,
            ...Default.shadow,
          }}
        >
          <TouchableOpacity onPress={() => navigation.pop()}>
            <Ionicons
              name={isRtl ? "arrow-forward" : "arrow-back"}
              size={23}
              color={Colors.black}
            />
          </TouchableOpacity>
          <Text
            style={{
              ...Fonts.Bold20black,
              marginHorizontal: Default.fixPadding * 1.5,
            }}
          >
            {tr("fundTransfer")}
          </Text>
        </View>
        <Text
          style={{
            ...Fonts.Bold16black,
            textAlign: "center",
            marginHorizontal: Default.fixPadding * 2,
            marginTop: Default.fixPadding * 1.5,
            marginBottom: Default.fixPadding * 0.5,
          }}
        >
          Transfer to:
        </Text>
        <View>
          <FlatList
            numColumns={3}
            data={fundTransferList}
            keyExtractor={(item) => item.key}
            renderItem={renderItemFundTransfer}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: Default.fixPadding }}
          />
        </View>
        <ListFooterComponent />
        <TouchableOpacity
          onPress={() => navigation.push("successfully/successfullyScreen")}
          style={styles.transferNowBtn}
        >
          <Text style={{ ...Fonts.Bold18white }}>{tr("transferNow")}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default FundTransferScreen;

const styles = StyleSheet.create({
  transferNowBtn: {
    justifyContent: "center",
    alignItems: "center",
    padding: Default.fixPadding * 1.2,
    margin: Default.fixPadding * 2,
    borderRadius: 10,
    backgroundColor: Colors.primary,
    ...Default.shadowBtn,
  },
  textInput: {
    flex: 1,
    paddingVertical: Default.fixPadding * 1.2,
    paddingHorizontal: Default.fixPadding * 1.5,
    marginBottom: Default.fixPadding * 1.5,
    marginHorizontal: Default.fixPadding * 2,
    borderRadius: 10,
    backgroundColor: Colors.white,
    ...Default.shadow,
  },
  bottomSheet: {
    overflow: "hidden",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: Colors.white,
    ...Default.shadow,
  },
});
