ninja: Entering directory `C:\Users\<USER>\Desktop\safaribank\android\app\.cxx\Debug\6c3h02c1\armeabi-v7a'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring done
-- Generating done
-- Build files have been written to: C:/Users/<USER>/Desktop/safaribank/android/app/.cxx/Debug/6c3h02c1/armeabi-v7a
[0/2] Re-checking globbed directories...
[1/4] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[2/4] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[3/4] Building CXX object CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/safaribank/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[4/4] Linking CXX shared library "C:\Users\<USER>\Desktop\safaribank\android\app\build\intermediates\cxx\Debug\6c3h02c1\obj\armeabi-v7a\libappmodules.so"
