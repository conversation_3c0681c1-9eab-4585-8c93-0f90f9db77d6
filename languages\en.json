{"onboardingScreen": {"title1": "Safe and fast banking operations", "title2": "Deposit Money from Any Bank or Any Mobile Money of your choice into SafariBank", "title3": "Withdraw money from Any bank and atm around you", "description": "SafariBank is a full digital bank that allows you easily and effectively manage your all financies under one platform, and connect your wallets and bank accounts for easy management.", "skip": "<PERSON><PERSON>", "tapBack": "Tap back again to exit the App"}, "loginScreen": {"login": "<PERSON><PERSON>", "welcomeBack": "Welcome back", "happy": "We are happy to see you again", "mobileNumber": "Enter your mobile number", "search": "Search", "close": "Close", "tapBack": "Tap back again to exit the App", "register": "Register", "termsAndConditions": "Terms and Conditions", "noAccount": "Don't have an account?", "registerHere": "Register now"}, "registerScreen": {"register": "Register", "name": "Enter your name", "number": "Enter your mobile number", "email": "Enter your email address"}, "otpScreen": {"verification": "OTP verification", "pleaseEnter": "Otp has been sent to you on your mobile number please enter it below ", "verify": "Verify", "resend": "Resend"}, "pinScreen": {"enterPin": "Enter pin", "welcome": "Welcome", "enterDigit": "<PERSON> enter 4 digit pin ", "continue": "Continue"}, "loader": {"pleaseWait": "Please wait"}, "bottomTab": {"home": "Home", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "loans": "Loans", "account": "Account", "tapBack": "Tap back again to exit the App"}, "homeScreen": {"totalBalance": "Total Balance", "savingAccount": "Saving account", "services": "Services", "account": "Account", "fundTransfer": "Fund transfer", "statement": "Statement", "billPay": "Bill pay", "scan": "Scan and pay", "invoices": "Invoices", "withdraw": "Withdraw", "more": "More", "businessSummary": "Business Summary", "seeAll": "See all"}, "servicesScreen": {"services": "Services", "account": "Account", "fundTransfer": "Fund transfer", "statement": "Statement", "billPay": "Bill pay", "scan": "Scan and pay", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "loans": "Loans", "cards": "Cards", "mutualFund": "Mutual fund", "insurance": "Insurance", "shopOffer": "Shop & offer", "recharge": "Recharge"}, "latestTransactionScreen": {"transaction": "Transaction"}, "notificationScreen": {"notification": "Notification", "remove": "Notification removed.", "noNotification": "No new notification"}, "fundTransferScreen": {"fundTransfer": "Fund transfer", "beneficiaryPay": "Beneficiary pay ", "payment": "IBAN payment ", "pay": "IMPS pay ", "safariBank": "SafariBank account", "otherBank": "Other Bank account", "mobile": "Mobile Money", "transferNow": "Transfer now"}, "beneficiaryPayScreen": {"fromAccount": "From account", "beneficiaryInfo": "SafariBank info", "name": "Beneficiary name", "bankName": "Beneficiary bank name", "accountNumber": "Universal Account Number", "amount": "Amount", "transferLimit": "Transfer limit", "savingAccount": "Saving account", "currentAccount": "Current account", "salaryAccount": "Salary account", "nriAccount": "Investment account"}, "paymentIBANScreen": {"fromAccount": "From account", "beneficiaryInfo": "Bank info", "number": "Account number", "name": "Beneficiary name", "code": "BIC code", "bank": "Select Bank Name", "amount": "Amount", "remark": "Any Description", "savingAccount": "Saving account", "currentAccount": "Current account", "salaryAccount": "Salary account", "nriAccount": "Investment account"}, "payIMPSScreen": {"fromAccount": "From account", "accountNo": "Select Account Number", "holderName": "Account holder name", "enterHolderName": "Enter account holder name", "toAccount": "Phone Number", "enterAccountNo": "Enter Phone Number", "mnoName": "Mobile Money Name", "mobileOperator": "Select Name", "iCode": "IFSC code", "enterICode": "Enter IFSC code", "amount": "Amount", "enterAmount": "Enter amount", "savingAccount": "Saving account", "currentAccount": "Current account", "salaryAccount": "Salary account", "nriAccount": "Investment account"}, "successfullyScreen": {"successfully": "Transfer successfully", "transferred": "Transferred to", "from": "From", "remark": "Remark", "amount": "Amount", "paymentMode": "Payment mode", "backToHome": "Back to home"}, "accountDetailScreen": {"account": "Account", "totalBalance": "Total balance", "accountNumber": "Account number", "accountDetail": "Account details", "cif": "CIF", "ifsc": "IFSC", "branchCode": "Branch code", "branchName": "Branch name", "openingDate": "Account opening date", "mmid": "MMId", "currentAccount": "Current account", "saving": "Saving account", "salaryAccount": "Salary account", "nRIAccount": "Investment account ", "viewStateMent": "View statement"}, "statementScreen": {"statement": "Statement", "totalBalance": "Total balance", "selectDate": "Select date", "go": "Go", "transaction": "Transaction result", "currentAccount": "Current account", "savingAccount": "Saving account", "salaryAccount": "Salary account", "nriAccount": "Investment account ", "cancel": "Cancel", "ok": "okay"}, "depositScreen": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "currentInvoices": "Current Invoices", "depositTo": "Deposit to:", "status": "Status:", "rate": "Rate:", "pending": "Pending", "completedInvoices": "Completed Invoices", "completed": "Completed", "totalInvoicesCreated": "Total Invoices", "totalpaid": "Total Paid", "receivables": "Total Receivables"}, "addDepositScreen": {"addDeposit": "Create Control Number", "controlNumberDetails": "Control Number gives your customer ability to pay you via a Universal Control Number. You can also use this to secure your business Loan", "amount": "Amount", "enterDeposit": "Enter deposit amount", "yourRate": "Put a correct Amount to be Paid Immediately at 5% Rate Only", "depositTo": "Deposit to", "selectAmount": "Select account", "customerPhoneNumber": "Customer Phone Number", "customerName": "Customer Name", "serviceOrProductDescription": "Service or Product Description", "create": "Create Now", "savingAccount": "Saving account", "currentAccount": "Current account", "salaryAccount": "Salary account", "nriAccount": "Investment account"}, "successDepositModal": {"success": "Success", "congratulation": "Congratulation your $1000 successfully deposited for 18 month", "okay": "Okay"}, "loansScreen": {"loans": "Loans", "makeEducation": "Third-party loans", "lowestInterest": "For Others", "theRightChoiceCar": "Personal Loans ", "dreamCar": "For You", "applyNow": "Apply now", "currentLoans": "Current loans", "businessLoan": "Business loan", "businessGrowth": "For business growth", "startupLoan": "Startup loan", "startupBusiness": "For new business", "period": "Period", "rate": "Rate", "eMI": "EMI", "viewStatement": "View statement", "homeLoan": "Home loan", "carLoan": "Car loan", "loanAmount": "Loan amount", "accountNumber": "Account number", "applyForLoan": "Apply for loan", "loanCategory": "Loan category", "loanPurpose": "Loan purpose", "repaymentPeriod": "Repayment period", "vendorName": "Vendor name", "newVentureName": "New venture or Project name", "newVentureDescription": "New venture or Project description", "budget": "Budget", "thirdPartyLoan": "Third Party Loan", "referenceNumber": "Reference number", "enterReferenceNumber": "Enter reference number"}, "loansStatementScreen": {"statement": "statement", "period": "Period", "rate": "Rate", "eMI": "EMI", "recentTransaction": "Recent transaction"}, "educationLoanScreen": {"educationLoan": "Education loan", "makeEducation": "Make education your top priority ", "description": "Loan for e.... ", "phoneNumber": "Phone number", "enterNumber": "Enter your phone number", "message": "Message", "writeMessage": "Write your message", "interested": "I am interested"}, "educationLoanModal": {"educationLoan": "Education loan", "thankYou": "Thank you for interested education loan we will contact soon ", "okay": "Okay"}, "accountScreen": {"account": "Account", "nearbyBank": "Nearby bank ", "nearbyATMs": "Nearby ATMs", "changePin": "Change pin", "language": "Language", "PrivacyPolicy": "Privacy policy", "termsCondition": "Terms & condition", "customerSupport": "Customer support", "logout": "Logout"}, "editProfileScreen": {"editProfile": "Edit profile", "name": "Name", "enterName": "Enter your name", "email": "Email address", "enterEmail": "Enter your email address", "mobile": "Phone number", "enterMobile": "Enter your phone number", "update": "Update", "changeProfile": "Upload image", "camera": "Camera", "gallery": "Gallery", "remove": "Remove", "removeImage": "Profile photo removed.", "ok": "Ok", "deny": "You has denied to access Camera!!!"}, "nearByScreen": {"direction": "Direction", "youAreHere": "You are here", "nearbyBank": "Nearby bank "}, "changePinScreen": {"changePin": "Change pin", "currentPin": "Current pin", "enterCurrentPin": "Enter current pin", "newPin": "New pin", "enterNewPin": "Enter new pin", "confirmPin": "Confirm pin", "confirmNewPin": "Confirm your new pin", "reset": "Reset"}, "languageScreen": {"language": "Language", "update": "Update "}, "termsConditionScreen": {"termsAndCondition": "Terms and condition", "mainDescription": "Lorem ipsum dolor sit amet consectetur. Doxposueretellus za volutpat aliquam vestibulum accumsan ipsuodignissim nulla elementum orci a dictumst. Magna placerat xmorbiuf pharetra viverra. Nunc fames scelerisque ac faucibuxmattis tristique tellus. Sed ultricies massa lacus sagittntegelorem. At quisque semper sit aliquet proidiasollicitudinvulputate.Lorem ipsum dolor sit amet consectetur.Dolposueretellus a volutpat aliquam vestibulum accumsan ipsuTodignissim nulla elementum orci a dictumst.Magna placerat vmorbiuf pharetra viverra.Nunc fames scelerisque ac faucibuszattis tristique tellus.Sed ultricies massa lacus sagitintegelorem.At quisque semper sit aliquet proidiasollicitudinvulputate.Tellus elementum ut ut morbi nunc lacus et.", "subDescription": "Lorem ipsum dolor sit amet consectetur. Doxposueretellus za volutpat aliquam vestibulum accumsan ipsuodignissim nulla elementum orci a dictumst. Magna placerat xmorbiuf pharetra viverra. Nunc fames scelerisque ac faucibuxmattis tristique tellus. Sed ultricies massa lacus sagittntegelorem. At quisque semper sit aliquet proidiasollicitudinvulputate. "}, "privacyPolicyScreen": {"privacyPolicy": "Privacy policy", "mainDescription": "Lorem ipsum dolor sit amet consectetur. Doxposueretellus za volutpat aliquam vestibulum accumsan ipsuodignissim nulla elementum orci a dictumst. Magna placerat xmorbiuf pharetra viverra. Nunc fames scelerisque ac faucibuxmattis tristique tellus. Sed ultricies massa lacus sagittntegelorem. At quisque semper sit aliquet proidiasollicitudinvulputate.Lorem ipsum dolor sit amet consectetur.Dolposueretellus a volutpat aliquam vestibulum accumsan ipsuTodignissim nulla elementum orci a dictumst.Magna placerat vmorbiuf pharetra viverra.Nunc fames scelerisque ac faucibuszattis tristique tellus.Sed ultricies massa lacus sagitintegelorem.At quisque semper sit aliquet proidiasollicitudinvulputate.Tellus elementum ut ut morbi nunc lacus et.", "subDescription": "Lorem ipsum dolor sit amet consectetur. Doxposueretellus za volutpat aliquam vestibulum accumsan ipsuodignissim nulla elementum orci a dictumst. Magna placerat xmorbiuf pharetra viverra. Nunc fames scelerisque ac faucibuxmattis tristique tellus. Sed ultricies massa lacus sagittntegelorem. At quisque semper sit aliquet proidiasollicitudinvulputate. "}, "customerSupportScreen": {"customerSupport": "Customer support", "weAreHere": "We are here to help so please get in touch with us.", "name": "Name", "enterName": "Enter your name", "email": "Email address", "enterEmail": "Enter your email address", "message": "Message", "enterMessage": "Enter message", "submit": "Submit"}, "logoutModal": {"logout": "Logout", "areYouSure": "Are you sur you want to logout?", "cancel": "Cancel", "yes": "Yes"}, "payScreen": {"pay": "Pay", "payTo": "Pay to", "amount": "Amount", "enterAmount": "Enter amount", "remark": "Remark", "enterRemark": "Enter remark", "payNow": "Pay now", "payApprovals": "Payment & Approvals", "paymentApprovals": "Approvals", "quickPayment": "Quick payment", "recentTransactions": "Recent transactions", "uploadQRCode": "Upload QR code", "enterMerchantID": "Enter merchant ID", "pendingApprovals": "Pending approvals"}, "cardScreen": {"cards": "Cards Management", "totalCards": "Total", "active": "Active", "activeCards": "Active cards", "blockedCards": "Blocked Cards"}, "addNewCard": {"createNewCard": "Create New Card", "cardType": "Card Type", "physicalCard": "Physical Card", "virtualCard": "Virtual Card", "physicalCardNote": "This is a physical card that you can use to withdraw money from any ATM around you.", "allocationType": "Card Allocation", "allocateTostaff": "Allocate to staff", "createMyOwnCard": "Create My Own Card", "staffID": "Enter staff ID", "enterstaffID": "Enter staff ID", "dailyLimit": "Daily Limit", "dailyLimitPlaceholder": "Enter daily limit", "atmAccess": "ATM Access", "locationRestrictions": "Location Restrictions", "delivaryAddress": "Delivery Address", "delivaryAddressPlaceholder": "Enter delivery address", "delivaryNotes": "Delivery Notes", "delivaryNotesPlaceholder": "Enter delivery notes", "internationalPayments": "International Payments", "createCard": "Create Card"}, "withdrawScreen": {"bankBranch": "Bank Branch", "atm": "ATM", "wakala": "<PERSON><PERSON><PERSON>", "withdrawalAmount": "<PERSON><PERSON><PERSON> Amount", "enterAmount": "Enter withdrawal amount", "agentId": "Agent ID", "enterAgentId": "Enter agent ID", "wakalaName": "Wakala Name", "selectWakala": "Select wakala", "withdraw": "Withdraw", "bankName": "Bank name", "selectBank": "Select bank", "availableBalance": "Available Balance", "mobileNumber": "Mobile number", "withdrawMoney": "<PERSON><PERSON><PERSON> Money"}}