{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b7a298e4f795a18f3d41262269705afc\\transformed\\browser-1.6.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,264,374", "endColumns": "105,102,109,104", "endOffsets": "156,259,369,474"}, "to": {"startLines": "65,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6578,7140,7243,7353", "endColumns": "105,102,109,104", "endOffsets": "6679,7238,7348,7453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0218db25d639d0bf937ef580de527e1b\\transformed\\material-1.6.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,312,411,536,624,691,788,857,920,1007,1073,1133,1202,1263,1317,1432,1491,1551,1605,1677,1807,1895,1979,2087,2165,2241,2335,2402,2468,2541,2619,2705,2778,2856,2934,3009,3099,3174,3268,3366,3440,3517,3617,3670,3738,3827,3916,3978,4043,4106,4213,4311,4411,4510", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,83,98,124,87,66,96,68,62,86,65,59,68,60,53,114,58,59,53,71,129,87,83,107,77,75,93,66,65,72,77,85,72,77,77,74,89,74,93,97,73,76,99,52,67,88,88,61,64,62,106,97,99,98,79", "endOffsets": "223,307,406,531,619,686,783,852,915,1002,1068,1128,1197,1258,1312,1427,1486,1546,1600,1672,1802,1890,1974,2082,2160,2236,2330,2397,2463,2536,2614,2700,2773,2851,2929,3004,3094,3169,3263,3361,3435,3512,3612,3665,3733,3822,3911,3973,4038,4101,4208,4306,4406,4505,4585"}, "to": {"startLines": "2,34,42,43,44,69,70,75,80,82,83,84,85,86,87,88,89,90,91,92,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3056,3864,3963,4088,6976,7043,7530,7941,8072,8159,8225,8285,8354,8415,8469,8584,8643,8703,8757,8829,9185,9273,9357,9465,9543,9619,9713,9780,9846,9919,9997,10083,10156,10234,10312,10387,10477,10552,10646,10744,10818,10895,10995,11048,11116,11205,11294,11356,11421,11484,11591,11689,11789,12065", "endLines": "5,34,42,43,44,69,70,75,80,82,83,84,85,86,87,88,89,90,91,92,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,132", "endColumns": "12,83,98,124,87,66,96,68,62,86,65,59,68,60,53,114,58,59,53,71,129,87,83,107,77,75,93,66,65,72,77,85,72,77,77,74,89,74,93,97,73,76,99,52,67,88,88,61,64,62,106,97,99,98,79", "endOffsets": "273,3135,3958,4083,4171,7038,7135,7594,7999,8154,8220,8280,8349,8410,8464,8579,8638,8698,8752,8824,8954,9268,9352,9460,9538,9614,9708,9775,9841,9914,9992,10078,10151,10229,10307,10382,10472,10547,10641,10739,10813,10890,10990,11043,11111,11200,11289,11351,11416,11479,11586,11684,11784,11883,12140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\73718788808fe748888117607a7f7695\\transformed\\appcompat-1.7.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "278,386,489,600,686,791,904,987,1066,1157,1250,1345,1439,1539,1632,1727,1822,1913,2004,2085,2198,2304,2402,2515,2620,2724,2882,12649", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "381,484,595,681,786,899,982,1061,1152,1245,1340,1434,1534,1627,1722,1817,1908,1999,2080,2193,2299,2397,2510,2615,2719,2877,2976,12726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\960e1087eeccdf840de6823a1e7fc27f\\transformed\\ui-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,386,489,579,659,755,845,932,1021,1112,1184,1262,1340,1415,1494,1564", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,77,77,74,78,69,120", "endOffsets": "196,282,381,484,574,654,750,840,927,1016,1107,1179,1257,1335,1410,1489,1559,1680"}, "to": {"startLines": "45,46,66,67,68,78,79,130,131,135,136,140,144,147,149,154,155,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4176,4272,6684,6783,6886,7765,7845,11888,11978,12309,12398,12731,13050,13285,13441,13849,13928,14078", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,77,77,74,78,69,120", "endOffsets": "4267,4353,6778,6881,6971,7840,7936,11973,12060,12393,12484,12798,13123,13358,13511,13923,13993,14194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6f597dc2fa8134c9a491d7aedff8ce\\transformed\\foundation-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "160,161", "startColumns": "4,4", "startOffsets": "14353,14441", "endColumns": "87,90", "endOffsets": "14436,14527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb5951a603c6ef86f8f4868bb3898655\\transformed\\react-android-0.76.9-debug\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,202,278,368,436,504,581,662,746,826,898,986,1073,1152,1233,1313,1390,1468,1542,1626,1700,1780,1851", "endColumns": "74,71,75,89,67,67,76,80,83,79,71,87,86,78,80,79,76,77,73,83,73,79,70,82", "endOffsets": "125,197,273,363,431,499,576,657,741,821,893,981,1068,1147,1228,1308,1385,1463,1537,1621,1695,1775,1846,1929"}, "to": {"startLines": "33,74,76,77,81,94,95,96,133,134,137,138,141,142,143,145,146,148,150,151,153,156,158,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2981,7458,7599,7675,8004,8959,9027,9104,12145,12229,12489,12561,12803,12890,12969,13128,13208,13363,13516,13590,13775,13998,14199,14270", "endColumns": "74,71,75,89,67,67,76,80,83,79,71,87,86,78,80,79,76,77,73,83,73,79,70,82", "endOffsets": "3051,7525,7670,7760,8067,9022,9099,9180,12224,12304,12556,12644,12885,12964,13045,13203,13280,13436,13585,13669,13844,14073,14265,14348"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1e754318e29fb6f6717a4ed39daeac16\\transformed\\play-services-basement-18.4.0\\res\\values-ka\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "55", "startColumns": "4", "startOffsets": "5368", "endColumns": "142", "endOffsets": "5506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4b5fbf6908f3e082e261c13278e081e6\\transformed\\play-services-base-18.5.0\\res\\values-ka\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,439,563,669,819,949,1067,1171,1340,1444,1595,1719,1876,2011,2073,2130", "endColumns": "100,144,123,105,149,129,117,103,168,103,150,123,156,134,61,56,71", "endOffsets": "293,438,562,668,818,948,1066,1170,1339,1443,1594,1718,1875,2010,2072,2129,2201"}, "to": {"startLines": "47,48,49,50,51,52,53,54,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4358,4463,4612,4740,4850,5004,5138,5260,5511,5684,5792,5947,6075,6236,6375,6441,6502", "endColumns": "104,148,127,109,153,133,121,107,172,107,154,127,160,138,65,60,75", "endOffsets": "4458,4607,4735,4845,4999,5133,5255,5363,5679,5787,5942,6070,6231,6370,6436,6497,6573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a43c68922964e2a138ddfb9cf7287a27\\transformed\\core-1.13.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "35,36,37,38,39,40,41,152", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3140,3236,3338,3437,3536,3642,3746,13674", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3231,3333,3432,3531,3637,3741,3859,13770"}}]}]}