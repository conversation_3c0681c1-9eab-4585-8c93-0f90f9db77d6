import React, { useState } from "react";
import {
    StyleSheet,
    Text,
    View,
    TextInput,
    TouchableOpacity,
    Dimensions,
    ScrollView,
} from "react-native";

import { TabView, SceneMap, TabBar } from "react-native-tab-view";
import { Colors, Default, Fonts } from "../../constants/styles";
import { Picker } from '@react-native-picker/picker';
import { useTranslation } from "react-i18next";

import Ionicons from "react-native-vector-icons/Ionicons";
import MyStatusBar from "../../components/myStatusBar";
import { useNavigation } from "expo-router";

const initialLayout = { width: Dimensions.get("window").width };

const BankBranch = () => {
    const { t } = useTranslation();

    //const isRtl = i18n.dir() == "rtl";

    function tr(key) {
        return t(`withdrawScreen:${key}`);
    }

    const [bankName, setBankName] = useState("");
    const [amount, setAmount] = useState("");

    return (
        <ScrollView contentContainerStyle={styles.container}>
            <Text style={styles.label}>{t("bankName")}</Text>
            <Picker
                selectedValue={bankName}
                onValueChange={(itemValue) => setBankName(itemValue)}
                style={styles.picker}
            >
                <Picker.Item label={t("withdrawScreen:selectBank")} value="" />
                <Picker.Item label="Bank A" value="bankA" />
                <Picker.Item label="Bank B" value="bankB" />
            </Picker>

            <Text style={styles.label}>{t("withdrawScreen:withdrawalAmount")}</Text>
            <TextInput
                value={amount}
                onChangeText={setAmount}
                keyboardType="numeric"
                placeholder={t("withdrawScreen:enterAmount")}
                style={styles.input}
            />
            <Text style={styles.balance}>{t("withdrawScreen:availableBalance")}: Tsh 10,000</Text>
            <TouchableOpacity onPress={() => setAmount("10000")} style={styles.maxButton}>
                <Text style={styles.maxButtonText}>{t("withdrawScreen:max")}</Text>
            </TouchableOpacity>
        </ScrollView>
    );
};

const Wakala = () => {
    const { t } = useTranslation();
    const [wakalaName, setWakalaName] = useState("");
    const [agentId, setAgentId] = useState("");
    const [amount, setAmount] = useState("");

    return (
        <ScrollView contentContainerStyle={styles.container}>
            <Text style={styles.label}>{t("withdrawScreen:wakalaName")}</Text>
            <Picker
                selectedValue={wakalaName}
                onValueChange={(itemValue) => setWakalaName(itemValue)}
                style={styles.picker}
            >
                <Picker.Item label={t("withdrawScreen:selectWakala")} value="" />
                <Picker.Item label="Wakala A" value="wakalaA" />
                <Picker.Item label="Wakala B" value="wakalaB" />
            </Picker>

            <Text style={styles.label}>{t("withdrawScreen:agentId")}</Text>
            <TextInput
                value={agentId}
                onChangeText={setAgentId}
                placeholder={t("withdrawScreen:enterAgentId")}
                style={styles.input}
            />

            <Text style={styles.label}>{t("withdrawScreen:withdrawalAmount")}</Text>
            <TextInput
                value={amount}
                onChangeText={setAmount}
                keyboardType="numeric"
                placeholder={t("withdrawScreen:enterAmount")}
                style={styles.input}
            />
            <Text style={styles.balance}>{t("withdrawScreen:availableBalance")}: Tsh 10,000</Text>
            <TouchableOpacity onPress={() => setAmount("10000")} style={styles.maxButton}>
                <Text style={styles.maxButtonText}>{t("withdrawScreen:max")}</Text>
            </TouchableOpacity>
        </ScrollView>
    );
};

const ATM = () => {
    const { t } = useTranslation();
    const [bankName, setBankName] = useState("");
    const [mobileNumber, setMobileNumber] = useState("");
    const [amount, setAmount] = useState("");

    return (
        <ScrollView contentContainerStyle={styles.container}>
            <Text style={styles.label}>{t("withdrawScreen:bankName")}</Text>
            <Picker
                selectedValue={bankName}
                onValueChange={(itemValue) => setBankName(itemValue)}
                style={styles.picker}
            >
                <Picker.Item label={t("withdrawScreen:selectBank")} value="" />
                <Picker.Item label="Bank A" value="bankA" />
                <Picker.Item label="Bank B" value="bankB" />
            </Picker>

            <Text style={styles.label}>{t("withdrawScreen:mobileNumber")}</Text>
            <TextInput
                value={mobileNumber}
                onChangeText={setMobileNumber}
                keyboardType="phone-pad"
                placeholder={t("withdrawScreen:enterMobileNumber")}
                style={styles.input}
            />

            <Text style={styles.label}>{t("withdrawScreen:withdrawalAmount")}</Text>
            <TextInput
                value={amount}
                onChangeText={setAmount}
                keyboardType="numeric"
                placeholder={t("withdrawScreen:enterAmount")}
                style={styles.input}
            />
            <Text style={styles.balance}>{t("withdrawScreen:availableBalance")}: Tsh 10,000</Text>
            <TouchableOpacity onPress={() => setAmount("10000")} style={styles.maxButton}>
                <Text style={styles.maxButtonText}>{t("withdrawScreen:max")}</Text>
            </TouchableOpacity>
        </ScrollView>
    );
};

const renderScene = SceneMap({
    bankBranch: BankBranch,
    wakala: Wakala,
    atm: ATM,
});

const WithdrawScreen = () => {
    const { t, i18n } = useTranslation();
    const isRtl = i18n.dir() == "rtl";
    const navigation = useNavigation();
    const [index, setIndex] = useState(0);
    const [routes] = useState([
        { key: "bankBranch", title: t("withdrawScreen:bankBranch") },
        { key: "wakala", title: t("withdrawScreen:wakala") },
        { key: "atm", title: t("withdrawScreen:atm") },
    ]);

    return (
        <View style={{ flex: 1, backgroundColor: Colors.regularGrey }}>
            <MyStatusBar />
            <View
                style={{
                    flexDirection: isRtl ? "row-reverse" : "row",
                    alignItems: "center",
                    paddingVertical: Default.fixPadding * 1.2,
                    paddingHorizontal: Default.fixPadding * 2,
                    backgroundColor: Colors.regularGrey,
                    ...Default.shadow,
                }}
            >
                <TouchableOpacity onPress={() => navigation.pop()}>
                    <Ionicons
                        name={isRtl ? "arrow-forward" : "arrow-back"}
                        size={23}
                        color={Colors.black}
                    />
                </TouchableOpacity>
                <Text
                    style={{
                        ...Fonts.Bold20black,
                        marginHorizontal: Default.fixPadding * 1.5,
                    }}
                >
                    {t("withdrawScreen:withdrawMoney")}
                </Text>
            </View>

            <TabView
                navigationState={{ index, routes }}
                renderScene={renderScene}
                onIndexChange={setIndex}
                initialLayout={initialLayout}
                renderTabBar={(props) => (
                    <TabBar
                        {...props}
                        indicatorStyle={{ backgroundColor: Colors.primary }}
                        style={{ backgroundColor: Colors.primary }}
                        labelStyle={{ color: Colors.black }}
                    />
                )}
            />
            <View style={styles.footer}>
                <TouchableOpacity style={styles.confirmButton}>
                    <Text style={styles.confirmButtonText}>{t("withdrawScreen:confirm")}</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.cancelButton}>
                    <Text style={styles.cancelButtonText}>{t("withdrawScreen:cancel")}</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default WithdrawScreen;

const styles = StyleSheet.create({
    container: {
        padding: Default.fixPadding * 2,
    },
    label: {
        ...Fonts.Bold16black,
        marginBottom: Default.fixPadding * 0.5,
    },
    picker: {
        height: 50,
        width: "100%",
        backgroundColor: Colors.white,
        borderRadius: 10,
        marginBottom: Default.fixPadding * 2,
        ...Default.shadow,
    },
    input: {
        paddingVertical: Default.fixPadding * 1.2,
        paddingHorizontal: Default.fixPadding * 1.5,
        marginBottom: Default.fixPadding * 2,
        borderRadius: 10,
        backgroundColor: Colors.white,
        ...Default.shadow,
    },
    balance: {
        ...Fonts.SemiBold14grey,
        marginBottom: Default.fixPadding * 2,
    },
    maxButton: {
        alignSelf: "flex-end",
        padding: Default.fixPadding * 0.5,
        backgroundColor: Colors.primary,
        borderRadius: 5,
    },
    maxButtonText: {
        ...Fonts.Bold14white,
    },
    footer: {
        flexDirection: "row",
        justifyContent: "space-between",
        padding: Default.fixPadding * 2,
        backgroundColor: Colors.white,
        borderTopWidth: 1,
        borderTopColor: Colors.lightGrey,
    },
    confirmButton: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        padding: Default.fixPadding * 1.2,
        marginRight: Default.fixPadding,
        borderRadius: 10,
        backgroundColor: Colors.primary,
        ...Default.shadowBtn,
    },
    confirmButtonText: {
        ...Fonts.Bold18white,
    },
    cancelButton: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        padding: Default.fixPadding * 1.2,
        marginLeft: Default.fixPadding,
        borderRadius: 10,
        backgroundColor: Colors.grey,
        ...Default.shadowBtn,
    },
    cancelButtonText: {
        ...Fonts.Bold18white,
    },
});