{"logs": [{"outputFile": "com.camelcase.rnstarbank.app-mergeDebugResources-80:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4b5fbf6908f3e082e261c13278e081e6\\transformed\\play-services-base-18.5.0\\res\\values-lv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,686,824,951,1064,1166,1337,1442,1607,1738,1903,2054,2114,2178", "endColumns": "102,156,128,103,137,126,112,101,170,104,164,130,164,150,59,63,84", "endOffsets": "295,452,581,685,823,950,1063,1165,1336,1441,1606,1737,1902,2053,2113,2177,2262"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4690,4797,4958,5091,5199,5341,5472,5589,5861,6036,6145,6314,6449,6618,6773,6837,6905", "endColumns": "106,160,132,107,141,130,116,105,174,108,168,134,168,154,63,67,88", "endOffsets": "4792,4953,5086,5194,5336,5467,5584,5690,6031,6140,6309,6444,6613,6768,6832,6900,6989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a43c68922964e2a138ddfb9cf7287a27\\transformed\\core-1.13.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "36,37,38,39,40,41,42,154", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3368,3466,3568,3668,3769,3876,3984,14126", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "3461,3563,3663,3764,3871,3979,4094,14222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0218db25d639d0bf937ef580de527e1b\\transformed\\material-1.6.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,279,360,461,595,678,743,837,910,971,1096,1164,1225,1297,1357,1411,1531,1591,1653,1707,1784,1914,2001,2083,2194,2274,2359,2450,2517,2583,2657,2738,2822,2895,2972,3049,3123,3216,3291,3381,3472,3544,3622,3713,3767,3835,3919,4006,4068,4132,4195,4305,4418,4521,4633", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,80,100,133,82,64,93,72,60,124,67,60,71,59,53,119,59,61,53,76,129,86,81,110,79,84,90,66,65,73,80,83,72,76,76,73,92,74,89,90,71,77,90,53,67,83,86,61,63,62,109,112,102,111,76", "endOffsets": "274,355,456,590,673,738,832,905,966,1091,1159,1220,1292,1352,1406,1526,1586,1648,1702,1779,1909,1996,2078,2189,2269,2354,2445,2512,2578,2652,2733,2817,2890,2967,3044,3118,3211,3286,3376,3467,3539,3617,3708,3762,3830,3914,4001,4063,4127,4190,4300,4413,4516,4628,4705"}, "to": {"startLines": "2,35,43,44,45,71,72,77,82,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3287,4099,4200,4334,7386,7451,7937,8336,8465,8590,8658,8719,8791,8851,8905,9025,9085,9147,9201,9278,9639,9726,9808,9919,9999,10084,10175,10242,10308,10382,10463,10547,10620,10697,10774,10848,10941,11016,11106,11197,11269,11347,11438,11492,11560,11644,11731,11793,11857,11920,12030,12143,12246,12535", "endLines": "6,35,43,44,45,71,72,77,82,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,134", "endColumns": "12,80,100,133,82,64,93,72,60,124,67,60,71,59,53,119,59,61,53,76,129,86,81,110,79,84,90,66,65,73,80,83,72,76,76,73,92,74,89,90,71,77,90,53,67,83,86,61,63,62,109,112,102,111,76", "endOffsets": "324,3363,4195,4329,4412,7446,7540,8005,8392,8585,8653,8714,8786,8846,8900,9020,9080,9142,9196,9273,9403,9721,9803,9914,9994,10079,10170,10237,10303,10377,10458,10542,10615,10692,10769,10843,10936,11011,11101,11192,11264,11342,11433,11487,11555,11639,11726,11788,11852,11915,12025,12138,12241,12353,12607"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1e754318e29fb6f6717a4ed39daeac16\\transformed\\play-services-basement-18.4.0\\res\\values-lv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "161", "endOffsets": "356"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5695", "endColumns": "165", "endOffsets": "5856"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee6f597dc2fa8134c9a491d7aedff8ce\\transformed\\foundation-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "162,163", "startColumns": "4,4", "startOffsets": "14799,14889", "endColumns": "89,89", "endOffsets": "14884,14974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb5951a603c6ef86f8f4868bb3898655\\transformed\\react-android-0.76.9-debug\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,215,290,360,442,510,580,659,741,825,908,978,1063,1144,1221,1303,1384,1460,1536,1611,1698,1776,1856,1928", "endColumns": "73,85,74,69,81,67,69,78,81,83,82,69,84,80,76,81,80,75,75,74,86,77,79,71,73", "endOffsets": "124,210,285,355,437,505,575,654,736,820,903,973,1058,1139,1216,1298,1379,1455,1531,1606,1693,1771,1851,1923,1997"}, "to": {"startLines": "34,48,76,78,79,83,96,97,98,135,136,139,140,143,144,145,147,148,150,152,153,155,158,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3213,4604,7862,8010,8080,8397,9408,9478,9557,12612,12696,12951,13021,13262,13343,13420,13578,13659,13812,13964,14039,14227,14451,14653,14725", "endColumns": "73,85,74,69,81,67,69,78,81,83,82,69,84,80,76,81,80,75,75,74,86,77,79,71,73", "endOffsets": "3282,4685,7932,8075,8157,8460,9473,9552,9634,12691,12774,13016,13101,13338,13415,13497,13654,13730,13883,14034,14121,14300,14526,14720,14794"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\73718788808fe748888117607a7f7695\\transformed\\appcompat-1.7.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "329,449,559,668,754,858,980,1062,1142,1252,1360,1466,1575,1686,1789,1901,2008,2113,2213,2298,2407,2518,2617,2728,2835,2940,3114,13106", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "444,554,663,749,853,975,1057,1137,1247,1355,1461,1570,1681,1784,1896,2003,2108,2208,2293,2402,2513,2612,2723,2830,2935,3109,3208,13184"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\960e1087eeccdf840de6823a1e7fc27f\\transformed\\ui-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,388,491,581,667,755,848,932,1017,1104,1177,1253,1330,1406,1484,1552", "endColumns": "97,88,95,102,89,85,87,92,83,84,86,72,75,76,75,77,67,121", "endOffsets": "198,287,383,486,576,662,750,843,927,1012,1099,1172,1248,1325,1401,1479,1547,1669"}, "to": {"startLines": "46,47,68,69,70,80,81,132,133,137,138,142,146,149,151,156,157,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4417,4515,7097,7193,7296,8162,8248,12358,12451,12779,12864,13189,13502,13735,13888,14305,14383,14531", "endColumns": "97,88,95,102,89,85,87,92,83,84,86,72,75,76,75,77,67,121", "endOffsets": "4510,4599,7188,7291,7381,8243,8331,12446,12530,12859,12946,13257,13573,13807,13959,14378,14446,14648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b7a298e4f795a18f3d41262269705afc\\transformed\\browser-1.6.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,373", "endColumns": "102,98,115,101", "endOffsets": "153,252,368,470"}, "to": {"startLines": "67,73,74,75", "startColumns": "4,4,4,4", "startOffsets": "6994,7545,7644,7760", "endColumns": "102,98,115,101", "endOffsets": "7092,7639,7755,7857"}}]}]}