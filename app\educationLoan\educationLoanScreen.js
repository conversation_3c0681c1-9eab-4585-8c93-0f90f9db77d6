import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
  ScrollView,
  Dimensions,
  TextInput,
  Switch,
  Modal,
} from "react-native";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Colors, Default, Fonts } from "../../constants/styles";
import Ionicons from "react-native-vector-icons/Ionicons";
import DashedLine from "react-native-dashed-line";
import MyStatusBar from "../../components/myStatusBar";
import { useNavigation } from "expo-router";
import { Picker } from '@react-native-picker/picker';

const { width } = Dimensions.get("window");

const LoanScreen = () => {
  const navigation = useNavigation();
  const { t, i18n } = useTranslation();
  const isRtl = i18n.dir() == "rtl";

  function tr(key) {
    return t(`loansScreen:${key}`);
  }

  const [step, setStep] = useState(1);
  const [loanCategory, setLoanCategory] = useState("thirdParty");
  const [loanAmount, setLoanAmount] = useState("");
  const [loanPurpose, setLoanPurpose] = useState("");
  const [repaymentPeriod, setRepaymentPeriod] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [vendorName, setVendorName] = useState("");
  const [vendorAccountNumber, setVendorAccountNumber] = useState("");
  const [paymentPurpose, setPaymentPurpose] = useState("");
  const [businessImprovementDetails, setBusinessImprovementDetails] = useState("");
  const [fundedAreas, setFundedAreas] = useState("");
  const [newVentureName, setNewVentureName] = useState("");
  const [newVentureDescription, setNewVentureDescription] = useState("");
  const [marketAnalysis, setMarketAnalysis] = useState("");
  const [educationLoanModal, setEducationLoanModal] = useState(false);

  const handleNextStep = () => {
    if (step < 3) {
      setStep(step + 1);
    }
  };

  const handlePreviousStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleSubmit = () => {
    setEducationLoanModal(true);
  };

  return (
    <View style={{ flex: 1, backgroundColor: Colors.regularGrey }}>
      <MyStatusBar />
      <View
        style={{
          zIndex: 1,
          flexDirection: isRtl ? "row-reverse" : "row",
          alignItems: "center",
          paddingVertical: Default.fixPadding * 1.2,
          paddingHorizontal: Default.fixPadding * 2,
          backgroundColor: Colors.regularGrey,
          ...Default.shadow,
        }}
      >
        <TouchableOpacity onPress={() => navigation.pop()}>
          <Ionicons
            name={isRtl ? "arrow-forward" : "arrow-back"}
            size={23}
            color={Colors.black}
          />
        </TouchableOpacity>
        <Text
          style={{
            ...Fonts.Bold20black,
            marginHorizontal: Default.fixPadding * 1.5,
          }}
        >
          {tr("applyForLoan")}
        </Text>
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        automaticallyAdjustKeyboardInsets={true}
      >
        {step === 1 && (
          <View style={{ marginHorizontal: Default.fixPadding * 2 }}>
            <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
              {tr("loanCategory")}
            </Text>
            <Picker
              selectedValue={loanCategory}
              onValueChange={(itemValue) => setLoanCategory(itemValue)}
              style={styles.picker}
            >
              <Picker.Item label={tr("thirdPartyLoan")} value="thirdParty" />
              <Picker.Item label={tr("businessLoan")} value="business" />
              <Picker.Item label={tr("startupLoan")} value="startup" />
            </Picker>

            <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
              {tr("loanAmount")}
            </Text>
            <TextInput
              value={loanAmount}
              onChangeText={setLoanAmount}
              keyboardType="number-pad"
              placeholder={tr("enterAmount")}
              placeholderTextColor={Colors.grey}
              style={styles.textInput}
            />

            <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
              {tr("loanPurpose")}
            </Text>
            <TextInput
              value={loanPurpose}
              onChangeText={setLoanPurpose}
              placeholder={tr("enterPurpose")}
              placeholderTextColor={Colors.grey}
              style={styles.textInput}
            />
            {loanCategory === "thirdParty" && (
              <>
                <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
                  {tr("referenceNumber")}
                </Text>

                <TextInput
                  value={repaymentPeriod}
                  onChangeText={setRepaymentPeriod}
                  keyboardType="number-pad"
                  placeholder={tr("enterReferenceNumber")}
                  placeholderTextColor={Colors.grey}
                  style={styles.textInput}
                />

              </>
            )}

            {loanCategory === "business" && (
              <>
                <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
                  {tr("budget")}
                </Text>
                <Picker
                  selectedValue={businessImprovementDetails}
                  onValueChange={(itemValue) => setBusinessImprovementDetails(itemValue)}
                  style={styles.picker}
                >
                  <Picker.Item label={tr("smallBudget")} value="small" />
                  <Picker.Item label={tr("mediumBudget")} value="medium" />
                  <Picker.Item label={tr("largeBudget")} value="large" />
                </Picker>

                <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
                  {tr("repaymentPeriod")}
                </Text>
                <TextInput
                  value={repaymentPeriod}
                  onChangeText={setRepaymentPeriod}
                  keyboardType="number-pad"
                  placeholder={tr("enterRepaymentPeriod")}
                  placeholderTextColor={Colors.grey}
                  style={styles.textInput}
                />

              </>
            )}

            {loanCategory === "startup" && (
              <>
                <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
                  {tr("newVentureName")}
                </Text>
                <TextInput
                  value={newVentureName}
                  onChangeText={setNewVentureName}
                  placeholder={tr("enterNewVentureName")}
                  placeholderTextColor={Colors.grey}
                  style={styles.textInput}
                />

                <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
                  {tr("newVentureDescription")}
                </Text>
                <TextInput
                  value={newVentureDescription}
                  onChangeText={setNewVentureDescription}
                  placeholder={tr("enterNewVentureDescription")}
                  placeholderTextColor={Colors.grey}
                  style={styles.textInput}
                />

                <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
                  {tr("budget")}
                </Text>
                <Picker
                  selectedValue={businessImprovementDetails}
                  onValueChange={(itemValue) => setBusinessImprovementDetails(itemValue)}
                  style={styles.picker}
                >
                  <Picker.Item label={tr("smallBudget")} value="small" />
                  <Picker.Item label={tr("mediumBudget")} value="medium" />
                  <Picker.Item label={tr("largeBudget")} value="large" />
                </Picker>

                <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
                  {tr("repaymentPeriod")}
                </Text>
                <TextInput
                  value={repaymentPeriod}
                  onChangeText={setRepaymentPeriod}
                  keyboardType="number-pad"
                  placeholder={tr("enterRepaymentPeriod")}
                  placeholderTextColor={Colors.grey}
                  style={styles.textInput}
                />
              </>
            )}

            <TouchableOpacity
              onPress={handleNextStep}
              style={styles.nextButton}
            >
              <Text style={{ ...Fonts.Bold18white }}>{tr("next")}</Text>
            </TouchableOpacity>
          </View>
        )
        }

        {
          step === 2 && (
            <View style={{ marginHorizontal: Default.fixPadding * 2 }}>
              <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
                {tr("loanSummary")}
              </Text>
              {/* Display loan summary and repayment plan here */}
              <Text style={{ ...Fonts.SemiBold14grey, marginTop: Default.fixPadding * 2 }}>
                {tr("loanAmount")}: {loanAmount}
              </Text>
              <Text style={{ ...Fonts.SemiBold14grey, marginTop: Default.fixPadding * 2 }}>
                {tr("loanPurpose")}: {loanPurpose}
              </Text>
              <Text style={{ ...Fonts.SemiBold14grey, marginTop: Default.fixPadding * 2 }}>
                {tr("repaymentPeriod")}: {repaymentPeriod}
              </Text>
              <Text style={{ ...Fonts.SemiBold14grey, marginTop: Default.fixPadding * 2 }}>
                {tr("accountNumber")}: {accountNumber}
              </Text>
              {/* Add more summary details as needed */}

              <TouchableOpacity
                onPress={handleNextStep}
                style={styles.nextButton}
              >
                <Text style={{ ...Fonts.Bold18white }}>{tr("confirm")}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handlePreviousStep}
                style={styles.previousButton}
              >
                <Text style={{ ...Fonts.Bold18white }}>{tr("previous")}</Text>
              </TouchableOpacity>
            </View>
          )
        }

        {
          step === 3 && (
            <View style={{ marginHorizontal: Default.fixPadding * 2 }}>
              <Text style={{ ...Fonts.Bold17black, marginTop: Default.fixPadding * 2 }}>
                {tr("submissionStatus")}
              </Text>
              <Text style={{ ...Fonts.SemiBold14grey, marginTop: Default.fixPadding * 2 }}>
                {tr("loanSubmitted")}
              </Text>
              <Text style={{ ...Fonts.SemiBold14grey, marginTop: Default.fixPadding * 2 }}>
                {tr("loanProcessingTime")}
              </Text>

              <TouchableOpacity
                onPress={() => navigation.pop()}
                style={styles.finishButton}
              >
                <Text style={{ ...Fonts.Bold18white }}>{tr("finish")}</Text>
              </TouchableOpacity>
            </View>
          )
        }
      </ScrollView >

      <EducationLoanModal
        visible={educationLoanModal}
        educationLoanModalClose={() => setEducationLoanModal(false)}
        okayClickHandle={() => {
          setEducationLoanModal(false);
          navigation.pop();
        }}
      />
    </View >
  );
};

const EducationLoanModal = ({ visible, educationLoanModalClose, okayClickHandle }) => {
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={educationLoanModalClose}
    >
      <View style={styles.modalView}>
        <Text style={styles.modalText}>Your loan application has been submitted successfully!</Text>
        <TouchableOpacity
          style={styles.okayButton}
          onPress={okayClickHandle}
        >
          <Text style={styles.okayButtonText}>OK</Text>
        </TouchableOpacity>
      </View>
    </Modal>
  );
};

export default LoanScreen;

const styles = StyleSheet.create({
  textInput: {
    paddingVertical: Default.fixPadding * 1.2,
    paddingHorizontal: Default.fixPadding * 1.5,
    marginTop: Default.fixPadding * 0.8,
    marginBottom: Default.fixPadding * 2,
    borderRadius: 10,
    backgroundColor: Colors.white,
    ...Default.shadow,
  },
  picker: {
    height: 50,
    width: '100%',
    backgroundColor: Colors.white,
    borderRadius: 10,
    ...Default.shadow,
  },
  nextButton: {
    justifyContent: "center",
    alignItems: "center",
    padding: Default.fixPadding * 1.2,
    marginVertical: Default.fixPadding * 2,
    borderRadius: 10,
    backgroundColor: Colors.primary,
    ...Default.shadowBtn,
  },
  previousButton: {
    justifyContent: "center",
    alignItems: "center",
    padding: Default.fixPadding * 1.2,
    marginVertical: Default.fixPadding * 2,
    borderRadius: 10,
    backgroundColor: Colors.grey,
    ...Default.shadowBtn,
  },
  finishButton: {
    justifyContent: "center",
    alignItems: "center",
    padding: Default.fixPadding * 1.2,
    marginVertical: Default.fixPadding * 2,
    borderRadius: 10,
    backgroundColor: Colors.primary,
    ...Default.shadowBtn,
  },
  modalView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 22,
    backgroundColor: "white",
    padding: 35,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalText: {
    marginBottom: 15,
    textAlign: "center",
    ...Fonts.Bold18black,
  },
  okayButton: {
    backgroundColor: Colors.primary,
    borderRadius: 10,
    padding: 10,
    elevation: 2,
  },
  okayButtonText: {
    color: "white",
    ...Fonts.Bold18white,
  },
});