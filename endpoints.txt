Request Endpoint: POST /api/auth/login
{
  "loginCode": "123456",
  "deviceInfo": {
    "deviceId": "unique-device-identifier",
    "platform": "ios/android",
    "appVersion": "1.0.0"
  }
}
{
  "success": true,
  "data": {
    "userId": "user_12345",
    "sessionToken": "jwt_token_here",
    "userProfile": {
      "name": "<PERSON>",
      "customerId": "CUST001",
      "lastLoginDate": "2024-01-15T10:30:00Z"
    },
    "accounts": [
      {
        "accountId": "ACC001",
        "accountType": "Saving Account",
        "accountNumber": "SB-*******1231",
        "balance": "15000.00",
        "currency": "TSH"
      },
      {
        "accountId": "ACC002", 
        "accountType": "Current Account",
        "accountNumber": "SB-*******1232",
        "balance": "5000.00",
        "currency": "TSH"
      }
    ]
  },
  "message": "Login successful"
}
{
  "success": false,
  "error": {
    "code": "INVALID_LOGIN_CODE",
    "message": "Invalid login code provided"
  }
}

Request Endpoint: POST /api/transfer/initiate
{
  "transferType": "safariBank|otherBanks|mobile",
  "fromAccount": {
    "accountId": "ACC001",
    "accountNumber": "SB-*******1231"
  },
  "toAccount": {
    "accountNumber": "SB-*******5678",
    "bankName": "Safari Bank",
    "bankCode": "SAFARI001",
    "beneficiaryName": "Jane Doe",
    "iban": "*********************" // for other banks
  },
  "amount": "1000.00",
  "currency": "TSH",
  "remarks": "Payment for services",
  "transactionPin": "1234"
}
{
  "success": true,
  "data": {
    "transactionId": "TXN_789012345",
    "referenceNumber": "REF_ABC123",
    "status": "COMPLETED",
    "transferDetails": {
      "fromAccount": "SB-*******1231",
      "toAccount": "SB-*******5678",
      "beneficiaryName": "Jane Doe",
      "amount": "1000.00",
      "currency": "TSH",
      "transferFee": "50.00",
      "totalDeducted": "1050.00"
    },
    "timestamp": "2024-01-15T14:30:00Z",
    "newBalance": "13950.00"
  },
  "message": "Transfer completed successfully"
}
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_FUNDS",
    "message": "Insufficient balance in source account",
    "details": {
      "availableBalance": "500.00",
      "requestedAmount": "1000.00"
    }
  }
}

Request Endpoint: GET /api/statements/transactions
{
  "accountId": "ACC001",
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "page": 1,
  "limit": 20,
  "transactionType": "all|credit|debit"
}
{
  "success": true,
  "data": {
    "accountInfo": {
      "accountId": "ACC001",
      "accountNumber": "SB-*******1231",
      "accountType": "Saving Account",
      "currentBalance": "15000.00",
      "currency": "TSH"
    },
    "summary": {
      "totalIncome": "3729.00",
      "totalExpenses": "347.00",
      "transactionCount": 25,
      "period": {
        "startDate": "2024-01-01",
        "endDate": "2024-01-31"
      }
    },
    "transactions": [
      {
        "transactionId": "TXN_001",
        "type": "DEBIT",
        "category": "TRANSFER",
        "description": "Money transfer to Jeklin shah",
        "amount": "-140.00",
        "balance": "14860.00",
        "timestamp": "2024-01-15T10:30:00Z",
        "referenceNumber": "REF_XYZ789",
        "counterparty": {
          "name": "Jeklin shah",
          "accountNumber": "SB-*******9999"
        }
      },
      {
        "transactionId": "TXN_002", 
        "type": "CREDIT",
        "category": "DEPOSIT",
        "description": "Paypal deposit",
        "amount": "+640.00",
        "balance": "15500.00",
        "timestamp": "2024-01-14T15:45:00Z",
        "referenceNumber": "REF_ABC456",
        "counterparty": {
          "name": "Paypal",
          "accountNumber": "PAYPAL_001"
        }
      },
      {
        "transactionId": "TXN_003",
        "type": "DEBIT", 
        "category": "MOBILE_PAYMENT",
        "description": "Mobile payment to +91 *********",
        "amount": "-150.00",
        "balance": "15350.00",
        "timestamp": "2024-01-13T09:20:00Z",
        "referenceNumber": "REF_MOB123",
        "counterparty": {
          "name": "+91 *********",
          "phoneNumber": "+91 *********"
        }
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalRecords": 25,
      "hasNextPage": true
    }
  },
  "message": "Statements retrieved successfully"
}

Request Endpoint: GET /api/notifications

Request Parameters:
{
  "userId": "user_12345",
  "page": 1,
  "limit": 20,
  "status": "all|read|unread"
}

Response JSON (Success):
{
  "success": true,
  "data": {
    "unreadCount": 3,
    "notifications": [
      {
        "notificationId": "NOTIF_001",
        "title": "Loan application approved!",
        "description": "Your car loan application has been successfully approved",
        "type": "LOAN_APPROVAL",
        "status": "UNREAD",
        "timestamp": "2024-01-15T09:36:00Z",
        "priority": "HIGH",
        "actionRequired": false,
        "metadata": {
          "loanId": "LOAN_12345",
          "amount": "50000.00"
        }
      },
      {
        "notificationId": "NOTIF_002", 
        "title": "Loan EMI period expires!",
        "description": "Your car loan EMI is due in 3 days",
        "type": "PAYMENT_REMINDER",
        "status": "UNREAD",
        "timestamp": "2024-01-14T10:30:00Z",
        "priority": "MEDIUM",
        "actionRequired": true,
        "metadata": {
          "loanId": "LOAN_12345",
          "dueDate": "2024-01-18",
          "amount": "800.00"
        }
      },
      {
        "notificationId": "NOTIF_003",
        "title": "Transfer completed successfully",
        "description": "Your transfer of TSH 1,000 to Jane Doe was completed",
        "type": "TRANSACTION_SUCCESS",
        "status": "READ",
        "timestamp": "2024-01-13T14:20:00Z",
        "priority": "LOW",
        "actionRequired": false,
        "metadata": {
          "transactionId": "TXN_789012345",
          "amount": "1000.00",
          "recipient": "Jane Doe"
        }
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 2,
      "totalRecords": 4,
      "hasNextPage": true
    }
  },
  "message": "Notifications retrieved successfully"
}

Response JSON (Error):
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Invalid session token"
  }
}

Request Endpoint: PUT /api/notifications/mark-read

Request JSON:
{
  "notificationIds": ["NOTIF_001", "NOTIF_002"],
  "markAll": false
}

Response JSON (Success):
{
  "success": true,
  "data": {
    "updatedCount": 2,
    "newUnreadCount": 1
  },
  "message": "Notifications marked as read"
}
