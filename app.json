{"expo": {"name": "SafariBank", "slug": "SafariBanking", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"bundleIdentifier": "com.camelcase.rnstarbank", "buildNumber": "1.0.0", "supportsTablet": false, "googleServicesFile": "./GoogleService-Info.plist", "config": {"googleMapsApiKey": "GOOGLE_MAP_API_KEY_HERE"}}, "android": {"package": "com.camelcase.rnstarbank", "versionCode": 1, "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "googleServicesFile": "./google-services.json", "config": {"googleMaps": {"apiKey": "GOOGLE_MAP_API_KEY_HERE"}}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#2e7d32"}], ["expo-notifications", {"icon": "./assets/images/notification-icon.png", "color": "#2e7d32"}], "@react-native-firebase/app", "@react-native-firebase/messaging"], "experiments": {"typedRoutes": true}}}