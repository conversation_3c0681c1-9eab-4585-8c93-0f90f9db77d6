{"buildFiles": ["C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\safaribank\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\safaribank\\android\\app\\.cxx\\Debug\\6c3h02c1\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\safaribank\\android\\app\\.cxx\\Debug\\6c3h02c1\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "appmodules", "output": "C:\\Users\\<USER>\\Desktop\\safaribank\\android\\app\\build\\intermediates\\cxx\\Debug\\6c3h02c1\\obj\\armeabi-v7a\\libappmodules.so", "runtimeFiles": []}, "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_RNCWebViewSpec"}, "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_RNVectorIconsSpec"}, "react_codegen_pagerview::@7032a8921530ec438d60": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_pagerview"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rngesturehandler_codegen"}, "react_codegen_rnpicker::@e8bb2e9e833f47d0d516": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rnpicker", "output": "C:\\Users\\<USER>\\Desktop\\safaribank\\android\\app\\build\\intermediates\\cxx\\Debug\\6c3h02c1\\obj\\armeabi-v7a\\libreact_codegen_rnpicker.so", "runtimeFiles": []}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rnreanimated"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rnscreens", "output": "C:\\Users\\<USER>\\Desktop\\safaribank\\android\\app\\build\\intermediates\\cxx\\Debug\\6c3h02c1\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "runtimeFiles": []}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rnsvg", "output": "C:\\Users\\<USER>\\Desktop\\safaribank\\android\\app\\build\\intermediates\\cxx\\Debug\\6c3h02c1\\obj\\armeabi-v7a\\libreact_codegen_rnsvg.so", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_safeareacontext", "output": "C:\\Users\\<USER>\\Desktop\\safaribank\\android\\app\\build\\intermediates\\cxx\\Debug\\6c3h02c1\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.********\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.********\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}